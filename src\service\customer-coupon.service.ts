import { Provide, Inject } from '@midwayjs/core';
import { CustomerCoupon } from '../entity/customer-coupon.entity';
import { BaseService } from '../common/BaseService';
import { Op } from 'sequelize';
import { Coupon } from '../entity';
import { Service } from '../entity/service.entity';
import { ApplicableScope } from '../common/Constant';
import { CombinedChangeLogService } from './combined-change-log.service';
import { CouponUsageRecordService } from './coupon-usage-record.service';
import { CombinedChangeType } from '../entity/combined-change-log.entity';

@Provide()
export class CustomerCouponService extends BaseService<CustomerCoupon> {
  @Inject()
  combinedChangeLogService: CombinedChangeLogService;

  @Inject()
  couponUsageRecordService: CouponUsageRecordService;

  constructor() {
    super('用户代金券');
  }

  getModel() {
    return CustomerCoupon;
  }

  async findByCustomerId(customerId: number) {
    return await this.findAll({
      query: { customerId },
      include: ['coupon'],
    });
  }

  /** 根据代金券ID获取用户列表 */
  async getUsersByCoupon(opt: any) {
    const result = await this.findAll({
      query: opt.query,
      offset: opt.offset || 0,
      limit: opt.limit || 20,
      include: ['customer'],
    });
    return result;
  }

  /** 根据用户ID获取权益卡类型列表 */
  async getCardsByUser(opt: any) {
    const result = await this.findAll({
      query: opt.query,
      offset: opt.offset || 0,
      limit: opt.limit || 20,
      include: [Coupon],
    });
    return result;
  }

  /**
   * 获取用户可用的代金券列表
   * @param customerId 用户ID
   * @param serviceId 服务ID（可选）
   * @param amount 订单金额（可选）
   * @returns 可用的代金券列表，包含折扣后金额
   */
  async getAvailableCoupons(
    customerId: number,
    serviceId?: number,
    amount?: number
  ) {
    const now = new Date();

    // 查询用户未使用且未过期的代金券
    const result = await this.findAll({
      query: {
        customerId,
        status: 'active',
        // 查找未过期或无过期时间的代金券
        [Op.or]: [{ expiryTime: { [Op.gt]: now } }, { expiryTime: null }],
      },
      include: ['coupon'],
    });

    // 如果没有传入服务ID或金额，则直接返回所有未使用且未过期的代金券
    if (!serviceId || amount === undefined) {
      return result;
    }

    // 获取服务信息，用于判断代金券适用范围
    const service = await Service.findByPk(serviceId, {
      include: ['serviceType'],
    });

    if (!service) {
      throw new Error('服务不存在');
    }

    // 筛选可用的代金券并计算折扣后金额
    const availableCoupons = [];

    for (const userCoupon of result.list) {
      const coupon = userCoupon.coupon;

      // 检查金额是否满足使用门槛
      if (amount < coupon.threshold) {
        continue;
      }

      // 检查适用范围
      let isApplicable = false;

      switch (coupon.applicableScope) {
        case ApplicableScope.不限:
        case ApplicableScope.所有服务:
          isApplicable = true;
          break;

        case ApplicableScope.指定服务类别:
          // 检查服务类型是否在适用范围内
          if (service.serviceType && coupon.applicableServiceTypes) {
            isApplicable = coupon.applicableServiceTypes.includes(
              service.serviceType.type
            );
          }
          break;

        case ApplicableScope.指定服务品牌:
          // 检查服务品牌是否在适用范围内
          if (service.serviceType && coupon.applicableServiceCategories) {
            isApplicable = coupon.applicableServiceCategories.includes(
              service.serviceType.id
            );
          }
          break;

        case ApplicableScope.指定服务:
          // 检查服务是否在适用范围内
          if (coupon.applicableServices) {
            isApplicable = coupon.applicableServices.includes(service.id);
          }
          break;

        default:
          // 其他范围（商品相关）不适用于服务
          isApplicable = false;
      }

      if (isApplicable) {
        // 计算折扣后金额
        const discountedAmount = Math.max(0, amount - coupon.amount);

        // 添加到可用代金券列表
        availableCoupons.push({
          ...userCoupon.toJSON(),
          discountedAmount: discountedAmount.toFixed(2),
          discount: coupon.amount,
        });
      }
    }

    // 按折扣金额降序排序（优惠最多的排在前面）
    availableCoupons.sort((a, b) => b.discount - a.discount);

    return {
      list: availableCoupons,
      count: availableCoupons.length,
    };
  }

  async useCoupon(id: number, orderId: number, isAdditionalService = false) {
    const userCoupon = await this.findById(id);
    if (!userCoupon) {
      throw new Error('代金券不存在');
    }
    if (userCoupon.status === 'expired') {
      throw new Error('代金券已过期');
    }

    // 检查是否过期
    if (userCoupon.expiryTime && userCoupon.expiryTime < new Date()) {
      await userCoupon.update({ status: 'expired' });
      throw new Error('代金券已过期');
    }

    // 是否是不限制次数的卡
    const isUnlimited =
      userCoupon.remainTimes === null || userCoupon.remainTimes === -1;
    // 检查使用次数
    if (!isUnlimited && userCoupon.remainTimes <= 0) {
      await userCoupon.update({ status: 'used', remainTimes: 0 });
      throw new Error('代金券使用次数已用完');
    }

    const beforeRemainTimes = isUnlimited ? '不限' : userCoupon.remainTimes;
    const afterRemainTimes = isUnlimited
      ? '不限'
      : (beforeRemainTimes as number) - 1;
    const beforeStatus = userCoupon.status;
    let afterStatus = beforeStatus;

    const updateData: any = {
      remainTimes: isUnlimited ? -1 : afterRemainTimes,
    };

    // 如果使用后次数为0，更新状态为已用完
    if (afterRemainTimes === 0) {
      updateData.status = 'used';
      afterStatus = 'used';
    }

    // 更新代金券
    const updatedCoupon = await userCoupon.update({
      ...updateData,
      status: afterStatus,
    });

    // 创建使用记录（只有主订单才创建，追加服务订单因为外键约束问题暂不创建）
    if (!isAdditionalService) {
      await this.couponUsageRecordService.createUsageRecord(id, orderId);

      // 记录变更日志
      let logMessage = '用户使用代金券';
      if (userCoupon.remainTimes > 0 && updateData.remainTimes > 0) {
        logMessage = `用户使用代金券(剩余${updateData.remainTimes}次)`;
      } else if (updateData.remainTimes === 0) {
        logMessage = '用户使用代金券(次数用完)';
      }

      await this.combinedChangeLogService.createLog({
        /** 变更类型 */
        changeType: CombinedChangeType.COUPON_USE,
        /** 关联的用户代金券ID */
        userCouponId: id,
        /** 关联的代金券ID */
        couponId: userCoupon.couponId,
        /** 关联的用户ID */
        customerId: userCoupon.customerId,
        /** 关联的订单ID */
        orderId,
        /** 变更前状态 */
        beforeStatus,
        /** 变更后状态 */
        afterStatus,
        /** 变更前剩余次数 */
        beforeRemainTimes: String(beforeRemainTimes),
        /** 变更后剩余次数 */
        afterRemainTimes: String(afterRemainTimes),
        description: logMessage,
      });
    }

    return updatedCoupon;
  }

  async findByOrderId(orderId: number) {
    // 通过使用记录查找关联的代金券
    const usageRecords =
      await this.couponUsageRecordService.getOrderCouponUsage(orderId);
    if (usageRecords && usageRecords.list && usageRecords.list.length > 0) {
      const customerCouponId = usageRecords.list[0].customerCouponId;
      return await this.findById(customerCouponId);
    }
    return null;
  }

  /**
   * 检查并更新过期的代金券状态
   * 可以定时调用此方法，将过期的代金券状态更新为expired
   */
  async checkAndUpdateExpiredCoupons() {
    const now = new Date();
    const result = await this.findAll({
      query: {
        expiryTime: { [Op.lt]: now },
        status: 'active',
      },
    });

    const expiredCoupons = result.list;
    if (expiredCoupons && expiredCoupons.length > 0) {
      // 批量更新过期代金券状态并记录日志
      await Promise.all(
        expiredCoupons.map(async (coupon: CustomerCoupon) => {
          const updatedCoupon = await coupon.update({ status: 'expired' });

          // 记录变更日志
          await this.combinedChangeLogService.createLog({
            changeType: CombinedChangeType.COUPON_EXPIRE,
            userCouponId: updatedCoupon.id,
            couponId: updatedCoupon.couponId,
            customerId: updatedCoupon.customerId,
            beforeStatus: 'active',
            afterStatus: 'expired',
            beforeRemainTimes: String(updatedCoupon.remainTimes),
            afterRemainTimes: String(updatedCoupon.remainTimes),
            description: '代金券过期',
          });

          return updatedCoupon;
        })
      );
    }

    return expiredCoupons?.length || 0;
  }

  /**
   * 创建用户代金券
   * @param data 代金券数据
   * @param isAdminGrant 是否是后台发放
   * @param operatorId 操作人ID（后台发放时需要）
   */
  async createCoupon(data: any, isAdminGrant = false, operatorId?: number) {
    // 如果关联的代金券有使用次数限制，则设置剩余使用次数
    if (data.couponId) {
      const couponInfo = await Coupon.findByPk(data.couponId);
      if (couponInfo && couponInfo.usageLimit !== undefined) {
        data.remainTimes = couponInfo.usageLimit;
      }
    }

    // 创建代金券
    const coupon = await this.create(data);

    // 记录变更日志
    const beforeStatus = 'created';
    const afterStatus = 'active';
    const beforeRemainTimes = coupon.remainTimes?.toString() || '不限';
    const afterRemainTimes = coupon.remainTimes?.toString() || '不限';
    const changeType = isAdminGrant
      ? CombinedChangeType.COUPON_GRANT
      : CombinedChangeType.COUPON_PURCHASE;
    const description = isAdminGrant ? '后台发放代金券' : '用户购买代金券';
    await this.combinedChangeLogService.createLog({
      changeType,
      userCouponId: coupon.id,
      couponId: coupon.couponId,
      customerId: coupon.customerId,
      beforeStatus,
      afterStatus,
      beforeRemainTimes,
      afterRemainTimes,
      description,
    });

    return coupon;
  }

  /**
   * 后台禁用代金券
   * @param id 代金券ID
   * @param operatorId 操作人ID
   * @param description 描述
   */
  async disableCoupon(id: number, operatorId: number, description?: string) {
    const coupon = await this.findById(id);
    if (!coupon) {
      throw new Error('代金券不存在');
    }

    // 更新代金券状态为过期
    const updatedCoupon = await coupon.update({ status: 'expired' });

    // 记录变更日志
    const beforeStatus = updatedCoupon.status;
    const afterStatus = 'expired';
    await this.combinedChangeLogService.createLog({
      changeType: CombinedChangeType.COUPON_DISABLE,
      userCouponId: updatedCoupon.id,
      couponId: updatedCoupon.couponId,
      customerId: updatedCoupon.customerId,
      operatorId,
      beforeStatus,
      afterStatus,
      beforeRemainTimes: String(updatedCoupon.remainTimes),
      afterRemainTimes: String(updatedCoupon.remainTimes),
      description: description || '后台禁用代金券',
    });

    return updatedCoupon;
  }

  /**
   * 后台撤销代金券
   * @param id 代金券ID
   * @param operatorId 操作人ID
   * @param description 描述
   */
  async revokeCoupon(id: number, operatorId: number, description?: string) {
    const coupon = await this.findById(id);
    if (!coupon) {
      throw new Error('代金券不存在');
    }

    // 更新代金券状态为过期
    const updatedCoupon = await coupon.update({ status: 'expired' });

    // 记录变更日志
    const beforeStatus = updatedCoupon.status;
    const afterStatus = 'expired';
    await this.combinedChangeLogService.createLog({
      changeType: CombinedChangeType.COUPON_REVOKE,
      userCouponId: updatedCoupon.id,
      couponId: updatedCoupon.couponId,
      customerId: updatedCoupon.customerId,
      operatorId,
      beforeStatus,
      afterStatus,
      beforeRemainTimes: String(updatedCoupon.remainTimes),
      afterRemainTimes: String(updatedCoupon.remainTimes),
      description: description || '后台撤销代金券',
    });

    return updatedCoupon;
  }

  async addTimes(id: number, times: number, operatorId?: number) {
    const userCoupon = await this.findById(id);
    if (!userCoupon) {
      throw new Error('代金券不存在');
    }

    // 如果是不限次数的卡券，直接返回
    if (userCoupon.remainTimes === null || userCoupon.remainTimes === -1) {
      return userCoupon;
    }

    const beforeRemainTimes = userCoupon.remainTimes;
    const afterRemainTimes = beforeRemainTimes + times;
    const beforeStatus = userCoupon.status;
    let afterStatus = beforeStatus;

    const updateData: any = {
      remainTimes: afterRemainTimes,
    };

    // 如果代金券状态为已用完，但添加次数后不为0，则更新状态为有效
    if (userCoupon.status === 'used' && afterRemainTimes > 0) {
      updateData.status = 'active';
      afterStatus = 'active';
    }

    // 如果代金券已过期，但仍在添加次数，不改变其过期状态
    if (userCoupon.status === 'expired') {
      afterStatus = 'expired';
    }

    // 更新代金券
    const updatedCoupon = await userCoupon.update({
      ...updateData,
      status: afterStatus,
    });

    // 记录变更日志
    await this.combinedChangeLogService.createLog({
      changeType: CombinedChangeType.COUPON_ADD_TIMES,
      userCouponId: updatedCoupon.id,
      couponId: updatedCoupon.couponId,
      customerId: updatedCoupon.customerId,
      operatorId,
      beforeStatus,
      afterStatus,
      beforeRemainTimes: String(beforeRemainTimes),
      afterRemainTimes: String(afterRemainTimes),
      description: '退回优惠券次数',
    });

    return updatedCoupon;
  }
}
