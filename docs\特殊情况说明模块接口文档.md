# 特殊情况说明模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

---

## 员工端接口

### 1. 添加订单特殊情况说明
**接口地址：** `POST /openapi/order-special-notes`  
**接口描述：** 员工为订单添加特殊情况说明，包含文字描述和图片  
**是否需要认证：** 是  
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "content": "宠物比较紧张，需要额外安抚时间",
  "photos": [
    "https://example.com/special1.jpg",
    "https://example.com/special2.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| content | string | 是 | 特殊情况说明内容，最多1000字符 |
| photos | string[] | 否 | 特殊情况图片URL数组，最多9张 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "content": "宠物比较紧张，需要额外安抚时间",
    "photos": [
      "https://example.com/special1.jpg",
      "https://example.com/special2.jpg"
    ],
    "createdAt": "2024-01-01T14:00:00.000Z",
    "updatedAt": "2024-01-01T14:00:00.000Z",
    "order": {
      "id": 1,
      "sn": "ORD20240101001",
      "status": "服务中"
    },
    "employee": {
      "id": 1,
      "name": "李四",
      "phone": "13900139000"
    }
  }
}
```

### 2. 更新订单特殊情况说明
**接口地址：** `PUT /openapi/order-special-notes/{orderId}`  
**接口描述：** 员工更新订单的特殊情况说明  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求体：**
```json
{
  "employeeId": 1,
  "content": "宠物情况已稳定，服务正常进行",
  "photos": [
    "https://example.com/special3.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| content | string | 否 | 特殊情况说明内容，最多1000字符 |
| photos | string[] | 否 | 特殊情况图片URL数组，最多9张，传空数组表示清空图片 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "content": "宠物情况已稳定，服务正常进行",
    "photos": [
      "https://example.com/special3.jpg"
    ],
    "createdAt": "2024-01-01T14:00:00.000Z",
    "updatedAt": "2024-01-01T14:30:00.000Z",
    "order": {
      "id": 1,
      "sn": "ORD20240101001",
      "status": "服务中"
    },
    "employee": {
      "id": 1,
      "name": "李四",
      "phone": "13900139000"
    }
  }
}
```

### 3. 查询订单特殊情况说明
**接口地址：** `GET /openapi/order-special-notes/{orderId}`  
**接口描述：** 查询指定订单的特殊情况说明  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "content": "宠物比较紧张，需要额外安抚时间",
    "photos": [
      "https://example.com/special1.jpg",
      "https://example.com/special2.jpg"
    ],
    "createdAt": "2024-01-01T14:00:00.000Z",
    "updatedAt": "2024-01-01T14:00:00.000Z",
    "employee": {
      "id": 1,
      "name": "李四",
      "phone": "13900139000"
    }
  }
}
```

### 4. 删除订单特殊情况说明
**接口地址：** `DELETE /openapi/order-special-notes/{orderId}`  
**接口描述：** 员工删除订单的特殊情况说明  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求体：**
```json
{
  "employeeId": 1
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

---

## 管理端接口

### 1. 查询所有订单特殊情况说明
**接口地址：** `GET /admin/order-special-notes`  
**接口描述：** 管理端查询所有订单的特殊情况说明，支持分页和筛选  
**是否需要认证：** 是  
**适用端：** 管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| orderId | number | 否 | 订单ID筛选 |
| employeeId | number | 否 | 员工ID筛选 |
| keyword | string | 否 | 订单编号关键词搜索 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "employeeId": 1,
        "content": "宠物比较紧张，需要额外安抚时间",
        "photos": [
          "https://example.com/special1.jpg",
          "https://example.com/special2.jpg"
        ],
        "createdAt": "2024-01-01T14:00:00.000Z",
        "updatedAt": "2024-01-01T14:00:00.000Z",
        "order": {
          "id": 1,
          "sn": "ORD20240101001",
          "status": "服务中",
          "customerId": 1
        },
        "employee": {
          "id": 1,
          "name": "李四",
          "phone": "13900139000"
        }
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 10
  }
}
```

### 2. 查询特殊情况说明详情
**接口地址：** `GET /admin/order-special-notes/{id}`  
**接口描述：** 管理端查询特殊情况说明详情  
**是否需要认证：** 是  
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 特殊情况说明ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "content": "宠物比较紧张，需要额外安抚时间",
    "photos": [
      "https://example.com/special1.jpg",
      "https://example.com/special2.jpg"
    ],
    "createdAt": "2024-01-01T14:00:00.000Z",
    "updatedAt": "2024-01-01T14:00:00.000Z",
    "order": {
      "id": 1,
      "sn": "ORD20240101001",
      "status": "服务中"
    },
    "employee": {
      "id": 1,
      "name": "李四",
      "phone": "13900139000"
    }
  }
}
```

### 3. 查询指定订单的特殊情况说明
**接口地址：** `GET /admin/order-special-notes/order/{orderId}`  
**接口描述：** 管理端查询指定订单的特殊情况说明  
**是否需要认证：** 是  
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "content": "宠物比较紧张，需要额外安抚时间",
    "photos": [
      "https://example.com/special1.jpg",
      "https://example.com/special2.jpg"
    ],
    "createdAt": "2024-01-01T14:00:00.000Z",
    "updatedAt": "2024-01-01T14:00:00.000Z",
    "employee": {
      "id": 1,
      "name": "李四",
      "phone": "13900139000"
    }
  }
}
```

**注意：** 如果订单没有特殊情况说明，返回 `data: null`

### 4. 同步订单hasSpecialNote字段
**接口地址：** `POST /admin/order-special-notes/sync-order-flag`
**接口描述：** 管理端同步订单表的hasSpecialNote字段，确保与实际特殊情况说明记录一致
**是否需要认证：** 是
**适用端：** 管理端

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalOrders": 1000,
    "ordersWithSpecialNotes": 25,
    "message": "订单hasSpecialNote字段同步完成"
  }
}
```

---

## 订单表字段说明

为了提高订单查询效率，在订单表中新增了冗余字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hasSpecialNote | boolean | 是否有特殊情况说明，默认false |

**使用场景：**
- 订单列表查询时可直接显示是否有特殊情况，无需关联查询
- 前端可根据此字段决定是否显示"特殊情况"标识
- 对有特殊情况的订单可针对性发起详细查询

---

## 业务规则说明

1. **权限控制**：
   - 只有订单的服务员工才能添加、更新、删除特殊情况说明
   - 管理端可以查看所有订单的特殊情况说明

2. **数据限制**：
   - 特殊情况说明内容最多1000字符
   - 最多可上传9张图片
   - 每个订单每个员工只能有一条特殊情况说明记录

3. **操作限制**：
   - 同一订单同一员工不能重复添加特殊情况说明，需使用更新接口
   - 删除特殊情况说明时需要验证员工权限

4. **数据关联**：
   - 特殊情况说明与订单和员工关联
   - 订单删除时，相关特殊情况说明会被级联删除
   - 员工删除时，特殊情况说明的员工ID会被设置为NULL

5. **性能优化**：
   - 订单表增加hasSpecialNote冗余字段，避免列表查询时的关联查询
   - 创建、删除特殊情况说明时自动维护订单表的hasSpecialNote字段
   - 提供数据同步接口，确保历史数据的字段一致性
