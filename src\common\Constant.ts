export enum OrderStatus {
  待付款 = '待付款',
  待接单 = '待接单',
  待服务 = '待服务',
  已出发 = '已出发',
  服务中 = '服务中',
  已完成 = '已完成',
  已评价 = '已评价',
  已取消 = '已取消',
  退款中 = '退款中',
  已退款 = '已退款',
}

export enum OrderStatusChangeType {
  下单 = '下单',
  付款 = '付款',
  接单 = '接单',
  派单 = '派单',
  转单 = '转单',
  修改服务时间 = '修改服务时间',
  出发 = '出发',
  开始服务 = '开始服务',
  完成订单 = '完成订单',
  取消订单 = '取消订单',
  申请退款 = '申请退款',
  退款 = '退款',
}

/** 适用范围 */
export enum ApplicableScope {
  不限 = 'all',
  所有服务 = 'allServices',
  指定服务类别 = 'serviceType',
  指定服务品牌 = 'serviceCategory',
  指定服务 = 'service',
  所有商品 = 'allProducts',
  指定商品类别 = 'productCategory',
  指定商品 = 'product',
}

/**
 * 消息类型枚举
 */
export enum MessageType {
  SYSTEM = 'system',
  PLATFORM = 'platform',
  ORDER = 'order',
}

/**
 * 推送状态枚举
 */
export enum PushStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
}

/**
 * 推送类型枚举
 */
export enum PushType {
  WECHAT = 'wechat',
  SMS = 'sms',
  EMAIL = 'email',
}

/**
 * 投诉建议类型枚举
 */
export enum ComplaintCategory {
  COMPLAINT = 'complaint',
  SUGGESTION = 'suggestion',
}

/**
 * 投诉建议子类型枚举
 */
export enum ComplaintSubCategory {
  ORDER = 'order',
  EMPLOYEE = 'employee',
  PLATFORM = 'platform',
  SERVICE = 'service',
}

/**
 * 投诉建议状态枚举
 */
export enum ComplaintStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

/**
 * 员工职位枚举
 */
export enum EmployeePosition {
  洗护师 = 'XI_HU_SHI',
  美容师 = 'MEI_RONG_SHI',
}

/**
 * 职位可接服务类型映射
 * 直接使用serviceType.type字段的值
 */
export const POSITION_SERVICE_MAPPING = {
  [EmployeePosition.洗护师]: ['XI_HU'], // 洗护
  [EmployeePosition.美容师]: ['XI_HU', 'MEI_RONG'], // 洗护、美容
};

/**
 * 投诉建议常量配置
 */
export const COMPLAINT_CONSTANTS = {
  // 投诉建议类型配置
  categories: {
    COMPLAINT: 'complaint' as const,
    SUGGESTION: 'suggestion' as const,
  },

  // 投诉建议子类型配置
  subCategories: {
    ORDER: 'order' as const,
    EMPLOYEE: 'employee' as const,
    PLATFORM: 'platform' as const,
    SERVICE: 'service' as const,
  },

  // 状态配置
  statuses: {
    PENDING: 'pending' as const,
    PROCESSING: 'processing' as const,
    RESOLVED: 'resolved' as const,
    CLOSED: 'closed' as const,
  },

  // 状态中文映射
  statusLabels: {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭',
  },

  // 类型中文映射
  categoryLabels: {
    complaint: '投诉',
    suggestion: '建议',
  },

  // 子类型中文映射
  subCategoryLabels: {
    order: '订单投诉',
    employee: '人员投诉',
    platform: '平台建议',
    service: '服务建议',
  },

  // 分页配置
  pagination: {
    defaultPage: 1,
    defaultLimit: 20,
    maxLimit: 100,
  },

  // 图片限制
  photoLimits: {
    maxCount: 6,
    maxSize: 5 * 1024 * 1024, // 5MB
  },

  // 内容限制
  contentLimits: {
    titleMaxLength: 200,
    contentMaxLength: 2000,
    contactInfoMaxLength: 100,
  },
};

/**
 * 消息系统常量配置
 */
export const MESSAGE_CONSTANTS = {
  // 消息类型配置
  messageTypes: {
    SYSTEM: 'system' as const,
    PLATFORM: 'platform' as const,
    ORDER: 'order' as const,
  },

  // 消息模板代码
  templateCodes: {
    ORDER_NEW: 'ORDER_NEW',
    ORDER_ACCEPTED: 'ORDER_ACCEPTED',
    ORDER_COMPLETED: 'ORDER_COMPLETED',
    SYSTEM_MAINTENANCE: 'SYSTEM_MAINTENANCE',
    PLATFORM_POLICY: 'PLATFORM_POLICY',
  },

  // 分页配置
  pagination: {
    defaultPage: 1,
    defaultLimit: 20,
    maxLimit: 100,
  },

  // 消息保留时间（天）
  retentionDays: {
    system: 30, // 系统消息保留30天
    platform: 60, // 平台消息保留60天
    order: 90, // 订单消息保留90天
  },

  // 推送配置
  push: {
    enabled: true,
    channels: {
      wechat: true,
      sms: false,
      email: false,
    },
  },
};
