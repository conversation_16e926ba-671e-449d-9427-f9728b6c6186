# 轮播图管理接口文档

## 概述

轮播图管理功能，支持图片展示和跳转链接设置，通过优先级控制展示顺序。

## 数据库设计

### banners 表结构

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | INTEGER | 轮播图ID | 主键，自增 |
| imageURL | VARCHAR(255) | 图片链接 | 必填 |
| jumpType | ENUM | 跳转类型 | custom-自定义链接，activity-活动页，null-无跳转 |
| jumpLink | VARCHAR(255) | 跳转链接 | jumpType为custom时使用 |
| priority | TINYINT | 展示优先级 | 1-10，默认5，数值越大优先级越高 |
| createdAt | DATETIME | 创建时间 | 自动生成 |
| updatedAt | DATETIME | 更新时间 | 自动更新 |

## 管理端接口

### 1. 查询轮播图列表

**接口地址：** `GET /banners`

**请求参数：**
```json
{
  "current": 1,           // 当前页码，默认1
  "pageSize": 10,         // 每页数量，默认10
  "imageURL": "图片链接",  // 可选，图片链接模糊查询
  "jumpType": "custom",   // 可选，跳转类型筛选（custom/activity/null）
  "priority": 5           // 可选，优先级筛选
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "imageURL": "https://example.com/banner1.jpg",
        "jumpType": "activity",
        "jumpLink": null,
        "priority": 8,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      },
      {
        "id": 2,
        "imageURL": "https://example.com/banner2.jpg",
        "jumpType": null,
        "jumpLink": null,
        "priority": 7,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 2
  }
}
```

### 2. 查询轮播图详情

**接口地址：** `GET /banners/:id`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "imageURL": "https://example.com/banner1.jpg",
    "jumpType": "custom",
    "jumpLink": "https://example.com/custom-page",
    "priority": 8,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3. 创建轮播图

**接口地址：** `POST /banners`

**请求参数：**
```json
{
  "imageURL": "https://example.com/banner.jpg",  // 必填，图片链接
  "jumpType": "activity",                        // 可选，跳转类型（custom-自定义链接，activity-活动页，null-无跳转）
  "jumpLink": "https://example.com/custom",      // 可选，跳转链接（jumpType为custom时必填）
  "priority": 8                                  // 可选，优先级（1-10），默认5
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "imageURL": "https://example.com/banner.jpg",
    "jumpType": "activity",
    "jumpLink": null,
    "priority": 8,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 4. 更新轮播图

**接口地址：** `PUT /banners/:id`

**请求参数：**（所有字段都是可选的）
```json
{
  "imageURL": "https://example.com/new-banner.jpg",
  "jumpType": "custom",
  "jumpLink": "https://example.com/new-custom-page",
  "priority": 9
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 5. 删除轮播图

**接口地址：** `DELETE /banners/:id`

**功能说明：** 删除轮播图记录，同时删除对应的图片文件

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**注意事项：**
- 删除轮播图时会同步删除对应的图片文件
- 如果图片删除失败，不会影响轮播图记录的删除，只会记录错误日志

### 6. 更新轮播图优先级

**接口地址：** `PUT /banners/:id/priority`

**请求参数：**
```json
{
  "priority": 9  // 必填，优先级（1-10）
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 7. 获取已启用的轮播图列表

**接口地址：** `GET /banners/enabled/list`

**功能说明：** 获取所有轮播图，按优先级降序排列

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "imageURL": "https://example.com/banner1.jpg",
      "jumpType": "activity",
      "jumpLink": null,
      "priority": 9,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 2,
      "imageURL": "https://example.com/banner2.jpg",
      "jumpType": "custom",
      "jumpLink": "https://example.com/custom-page",
      "priority": 8,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 3,
      "imageURL": "https://example.com/banner3.jpg",
      "jumpType": null,
      "jumpLink": null,
      "priority": 7,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## 小程序端接口

### 1. 获取轮播图列表

**接口地址：** `GET /openapi/banners`

**功能说明：** 获取所有轮播图，按优先级降序排列，供小程序端展示

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "imageURL": "https://example.com/banner1.jpg",
      "jumpType": "activity",
      "jumpLink": null,
      "priority": 9
    },
    {
      "id": 2,
      "imageURL": "https://example.com/banner2.jpg",
      "jumpType": "custom",
      "jumpLink": "https://example.com/custom-page",
      "priority": 8
    },
    {
      "id": 3,
      "imageURL": "https://example.com/banner3.jpg",
      "jumpType": null,
      "jumpLink": null,
      "priority": 7
    }
  ]
}
```

## 业务规则

1. **优先级规则**：优先级范围为1-10，数值越大优先级越高，默认为5
2. **排序规则**：轮播图列表按优先级降序排列，优先级相同时按创建时间降序排列
3. **图片要求**：imageURL必须是有效的图片链接
4. **跳转类型规则**：
   - `jumpType` 为 `null` 时，表示无跳转逻辑，`jumpLink` 字段自动清空
   - `jumpType` 为 `custom` 时，`jumpLink` 字段必须填写
   - `jumpType` 为 `activity` 时，`jumpLink` 字段自动清空，小程序端自行处理活动页跳转
   - 跳转类型只能是 `custom`（自定义链接）、`activity`（活动页）或 `null`（无跳转）

## 小程序端处理说明

1. **无跳转**：当 `jumpType` 为 `null` 时，轮播图仅作展示，不响应点击事件
2. **自定义链接跳转**：当 `jumpType` 为 `custom` 时，小程序使用 `jumpLink` 字段进行页面跳转
3. **活动页跳转**：当 `jumpType` 为 `activity` 时，小程序自行处理跳转到活动详情页逻辑

## 前端处理示例

```javascript
// 轮播图点击处理
function handleBannerClick(banner) {
  if (!banner.jumpType) {
    // 无跳转类型，不处理
    return;
  }

  if (banner.jumpType === 'custom' && banner.jumpLink) {
    // 自定义链接跳转
    wx.navigateTo({ url: banner.jumpLink });
  } else if (banner.jumpType === 'activity') {
    // 活动页跳转，小程序自行处理
    wx.navigateTo({ url: '/pages/activity/index' });
  }
}
```

## 注意事项

1. 目前轮播图没有启用/禁用状态字段，所有轮播图都会在前端展示
2. 建议根据业务需要考虑是否需要添加启用/禁用功能
3. 图片上传功能需要配合文件上传接口使用
4. 跳转类型为可选字段，未设置时表示无跳转逻辑
5. 不再关联活动表，前端根据jumpType自行处理跳转逻辑
