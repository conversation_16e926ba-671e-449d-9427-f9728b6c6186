# 客户模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

---

## 1. 客户管理接口

### 1.1 查询客户列表
**接口地址：** `GET /customers`  
**接口描述：** 查询客户列表，支持分页和筛选  
**是否需要认证：** 是

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| phone | string | 否 | 手机号筛选（模糊查询） |
| nickname | string | 否 | 昵称筛选（模糊查询） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "张三",
        "phone": "13800138000",
        "memberStatus": 1,
        "points": 100,
        "status": "active",
        "createdAt": "2024-01-01T10:00:00.000Z"
      }
    ],
    "total": 100
  }
}
```

### 1.2 按ID查询客户
**接口地址：** `GET /customers/{id}`  
**接口描述：** 按ID查询客户详细信息  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "nickname": "张三",
    "phone": "13800138000",
    "memberStatus": 1,
    "points": 100,
    "status": "active",
    "createdAt": "2024-01-01T10:00:00.000Z"
  }
}
```

---

## 2. 宠物管理接口

### 2.1 获取客户的宠物列表
**接口地址：** `GET /customers/{id}/pets`  
**接口描述：** 获取指定客户的宠物列表  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "customerId": 1,
      "name": "小白",
      "avatar": "https://example.com/avatar.jpg",
      "type": "狗",
      "breed": "金毛",
      "gender": 1,
      "birthday": "2020-01-01T00:00:00.000Z",
      "bri": 48,
      "hairType": "long",
      "weight": 25.5,
      "weightType": "中型犬",
      "isVaccine": true,
      "isSterilization": false,
      "isRepellent": true,
      "orderIndex": 0
    }
  ]
}
```

### 2.2 新增宠物
**接口地址：** `POST /customers/{id}/pet`  
**接口描述：** 为指定客户新增宠物  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |

**请求体：**
```json
{
  "name": "小黑",
  "avatar": "https://example.com/avatar2.jpg",
  "type": "猫",
  "breed": "英短",
  "gender": 2,
  "birthday": "2021-06-01T00:00:00.000Z",
  "hairType": "short",
  "weight": 4.5,
  "weightType": "小型猫",
  "isVaccine": true,
  "isSterilization": true,
  "isRepellent": true,
  "orderIndex": 1
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 2,
    "customerId": 1,
    "name": "小黑",
    "avatar": "https://example.com/avatar2.jpg",
    "type": "猫",
    "breed": "英短",
    "gender": 2,
    "birthday": "2021-06-01T00:00:00.000Z",
    "bri": 30,
    "hairType": "short",
    "weight": 4.5,
    "weightType": "小型猫",
    "isVaccine": true,
    "isSterilization": true,
    "isRepellent": true,
    "orderIndex": 1
  }
}
```

### 2.3 更新宠物信息
**接口地址：** `PUT /customers/{id}/pet/{petId}`  
**接口描述：** 更新指定客户的指定宠物信息  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |
| petId | number | 是 | 宠物ID |

**请求体：**
```json
{
  "name": "小白白",
  "weight": 26.0,
  "isVaccine": true
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "customerId": 1,
    "name": "小白白",
    "weight": 26.0,
    "isVaccine": true
  }
}
```

### 2.4 删除宠物
**接口地址：** `DELETE /customers/{id}/pet/{petId}`  
**接口描述：** 删除指定客户的指定宠物  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |
| petId | number | 是 | 宠物ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 2.5 查询指定宠物最后一次洗护完成时间
**接口地址：** `GET /customers/{id}/pets/{petId}/last-service-time`  
**接口描述：** 查询指定客户的指定宠物最后一次洗护完成时间  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |
| petId | number | 是 | 宠物ID |

**响应示例（有洗护记录）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "petId": 1,
    "petName": "小白",
    "lastServiceTime": "2024-01-15T16:30:00.000Z",
    "orderId": 123,
    "orderSn": "ORD20240115001",
    "serviceTime": "2024-01-15T14:00:00.000Z"
  }
}
```

**响应示例（无洗护记录）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "petId": 1,
    "petName": "小白",
    "lastServiceTime": null,
    "message": "该宠物暂无完成的洗护记录"
  }
}
```

**字段说明：**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| petId | number | 宠物ID |
| petName | string | 宠物名称 |
| lastServiceTime | string\|null | 最后一次洗护完成时间，ISO 8601格式，无记录时为null |
| orderId | number | 最后一次洗护的订单ID（有记录时返回） |
| orderSn | string | 最后一次洗护的订单编号（有记录时返回） |
| serviceTime | string | 最后一次洗护的预约服务时间（有记录时返回） |
| message | string | 提示信息（无记录时返回） |

---

## 3. 地址管理接口

### 3.1 获取客户的地址列表
**接口地址：** `GET /customers/{id}/addresses`  
**接口描述：** 获取指定客户的地址列表  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "customerId": 1,
      "address": "北京市朝阳区xxx",
      "longitude": 116.123456,
      "latitude": 39.123456,
      "addressDetail": "xxx小区1号楼",
      "addressRemark": "门口有保安",
      "isDefault": true
    }
  ]
}
```

---

## 4. 位置服务接口

### 4.1 员工端上报当前位置
**接口地址：** `POST /openapi/location/updateEmployeeLocation`
**接口描述：** 员工端定时上报当前位置，自动更新关联车辆的位置信息
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "employeeId": 1,
  "latitude": 39.916527,
  "longitude": 116.397128,
  "address": "北京市朝阳区某某街道"
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| latitude | number | 是 | 纬度（-90到90之间） |
| longitude | number | 是 | 经度（-180到180之间） |
| address | string | 否 | 当前地址描述 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "employeeId": 1,
    "vehicleId": 5,
    "plateNumber": "京A12345",
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "北京市朝阳区某某街道",
    "updateTime": "2024-01-15T10:30:00.000Z",
    "message": "位置更新成功"
  }
}
```

### 4.2 计算两个经纬度之间的距离
**接口地址：** `GET /openapi/location/calculateDistance`
**接口描述：** 计算两个经纬度坐标之间的直线距离
**是否需要认证：** 否

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lat1 | number | 是 | 起点纬度 |
| lng1 | number | 是 | 起点经度 |
| lat2 | number | 是 | 终点纬度 |
| lng2 | number | 是 | 终点经度 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": 1250
}
```

**说明：** 返回值为距离，单位为米

### 4.3 查找附近车辆
**接口地址：** `GET /openapi/location/findNearbyVehicles`
**接口描述：** 根据指定位置查找附近的空闲车辆
**是否需要认证：** 否

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lat | number | 是 | 中心点纬度 |
| lng | number | 是 | 中心点经度 |
| radius | number | 否 | 搜索半径（米），默认10000米 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 5,
      "plateNumber": "京A12345",
      "vehicleType": "小型车",
      "latitude": 39.916527,
      "longitude": 116.397128,
      "status": "空闲",
      "distance": 850,
      "employee": {
        "id": 1,
        "name": "张师傅",
        "phone": "13800138000"
      }
    }
  ]
}
```

**字段说明：**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| distance | number | 与中心点的距离，单位为米 |
| status | string | 车辆状态（空闲/服务中） |

## 注意事项

1. 所有接口都需要进行身份认证（除特别说明的公开接口）
2. 时间字段均为ISO 8601格式（UTC+8时区）
3. 宠物年龄（bri）字段为虚拟字段，根据生日自动计算月数
4. 最后洗护时间基于订单的actualServiceEndTime字段，只统计状态为"已完成"的订单
5. 如果宠物不存在或不属于指定客户，将返回相应的错误信息
6. 位置坐标必须在有效范围内：纬度（-90到90），经度（-180到180）
7. 员工位置上报会自动更新关联车辆的位置信息
8. 员工必须先绑定车辆才能进行位置上报
