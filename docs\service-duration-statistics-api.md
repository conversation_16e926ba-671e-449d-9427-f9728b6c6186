# 服务时长统计接口文档

## 概述
本文档描述了主订单服务时长统计相关的API接口，基于订单状态变更记录计算实际服务时长。

## 管理端接口

### 1. 获取服务时长统计列表

**接口地址：** `GET /service-duration-statistics/list`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1,                     // 可选，员工ID
  "serviceId": 1,                      // 可选，服务ID
  "orderId": 1,                        // 可选，订单ID
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "orderId": 1,
        "orderSn": "20240101001",
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001"
        },
        "customer": {
          "id": 1,
          "nickname": "小明",
          "phone": "13800138002"
        },
        "services": [
          {
            "serviceId": 1,
            "serviceName": "宠物洗护",
            "quantity": 1
          }
        ],
        "serviceTime": "2024-01-01T10:00:00.000Z",
        "actualServiceStartTime": "2024-01-01T10:30:00.000Z",
        "actualServiceEndTime": "2024-01-01T11:30:00.000Z",
        "actualServiceDuration": 60,
        "totalFee": 100.00,
        "changeLogs": []
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

### 2. 获取服务时长概览统计

**接口地址：** `GET /service-duration-statistics/overview`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "totalOrders": 100,                // 总订单数
    "avgDuration": 75,                 // 平均服务时长（分钟）
    "minDuration": 30,                 // 最短服务时长（分钟）
    "maxDuration": 120,                // 最长服务时长（分钟）
    "totalDuration": 7500              // 总服务时长（分钟）
  }
}
```

### 3. 按服务类型统计服务时长

**接口地址：** `GET /service-duration-statistics/by-service-type`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": [
    {
      "serviceId": 1,
      "serviceName": "宠物洗护",
      "orderCount": 50,
      "totalDuration": 3750,
      "avgDuration": 75
    },
    {
      "serviceId": 2,
      "serviceName": "宠物美容",
      "orderCount": 30,
      "totalDuration": 3600,
      "avgDuration": 120
    }
  ]
}
```

### 4. 按员工统计服务时长

**接口地址：** `GET /service-duration-statistics/by-employee`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "employeeId": 1,
        "employeeName": "张师傅",
        "employeePhone": "13800138001",
        "orderCount": 50,
        "totalDuration": 3750,
        "avgDuration": 75
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```



## 字段说明

### 时间字段
- `serviceTime`: 预约服务时间，ISO 8601格式
- `actualServiceStartTime`: 实际服务开始时间，ISO 8601格式
- `actualServiceEndTime`: 实际服务结束时间，ISO 8601格式
- `actualServiceDuration`: 实际服务时长，单位为分钟

### 计算逻辑
- 服务时长基于订单状态变更记录中的"开始服务"和"完成订单"时间计算
- 只有已完成的订单才会有完整的服务时长数据
- 时长统计基于实际操作时间，不是预约时间

## 注意事项

1. 只统计主订单的服务时长，不包括增项服务
2. 时长计算基于员工实际操作的"开始服务"和"完成订单"动作
3. 所有时间均为服务器时间（UTC+8）
4. 分页查询默认按服务结束时间倒序排列
5. 如果订单没有完成，则不会显示在时长统计中
