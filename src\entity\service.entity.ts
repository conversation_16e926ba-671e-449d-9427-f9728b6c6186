import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
  BelongsToMany,
} from 'sequelize-typescript';
import { ServiceType } from './service-type.entity';
import { AdditionalService } from './additional-service.entity';
import { ServiceAdditional } from './service-additional.entity';
import { DictionaryCache } from '../common/dictionary-cache';

export interface ServiceAttributes {
  /** 服务ID */
  id: number;
  /** 服务类目ID */
  serviceTypeId: number;
  /** 服务名称 */
  serviceName: string;
  /** 服务logo */
  logo?: string;
  /** 服务描述 */
  description?: string;
  /** 基础价格 */
  basePrice: number;
  /** 宠物类型 */
  petTypes: string;
  /** 体型 */
  size?: string;
  /** 体重描述，虚拟字段，根据宠物类型和体型进行换算 */
  weightDescription?: string;
  /** 适用毛发类型 */
  hairType?: string;
  /** 是否按距离计费 */
  distanceChargeFlag: boolean;
  /** 是否支持权益卡抵扣 */
  cardDiscountFlag: boolean;
  /** 排序值 */
  orderIndex: number;
  /** 是否发布 */
  published: boolean;
  /** 平均服务时长（分钟） */
  avgDuration?: number;
  /** 关联的服务类型信息 */
  serviceType?: ServiceType;
}

@Table({ tableName: 'services', timestamps: true, comment: '服务项目表' })
export class Service
  extends Model<ServiceAttributes>
  implements ServiceAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '服务ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '服务类目ID',
  })
  @ForeignKey(() => ServiceType)
  serviceTypeId: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '服务名称',
  })
  serviceName: string;

  /** 服务logo */
  @Column({
    type: DataType.STRING(255),
    comment: '服务logo',
  })
  logo?: string;

  @Column({
    type: DataType.TEXT,
    comment: '服务描述',
  })
  description: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '基础价格',
  })
  basePrice: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '宠物类型',
  })
  petTypes: string;

  /** 体型 */
  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '体型，如小型犬、中体猫等，可选值通过字典配置',
  })
  size?: string;

  @Column({
    type: DataType.VIRTUAL,
    get() {
      const size = this.getDataValue('size');
      const hairType = this.getDataValue('hairType');
      // 使用全局缓存
      let str1 = '';
      try {
        const dictCache = DictionaryCache.getInstance();
        if (dictCache) {
          str1 = dictCache.getSync(size)?.description || '';
        }
      } catch (error) {
        console.error('获取字典缓存失败:', error);
      }
      let str2 = '';
      switch (hairType) {
        case 'short':
          str2 = '限短毛';
          break;
        case 'long':
          str2 = '限长毛';
          break;
        default:
          str2 = '无毛长限制';
          break;
      }
      return (str1 + ' ' + str2).trim();
    },
    comment: '体重描述，虚拟字段，根据宠物类型和体型进行换算',
  })
  weightDescription?: string;

  @Column({
    type: DataType.STRING(20),
    comment: '适用毛发类型',
  })
  hairType: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '是否按距离计费',
  })
  distanceChargeFlag: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
    comment: '是否支持权益卡抵扣',
  })
  cardDiscountFlag: boolean;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '排序值',
  })
  orderIndex: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否生效',
  })
  published: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '平均服务时长（分钟）',
  })
  avgDuration?: number;

  @BelongsTo(() => ServiceType, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  serviceType: ServiceType;

  @BelongsToMany(() => AdditionalService, () => ServiceAdditional)
  additionalServices: AdditionalService[];
}
