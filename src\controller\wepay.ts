/*
 * @Description: 微信支付
 * @Author: pengliang.zhu
 * @Date: 2025-04-16 22:49:05
 * @Last Modified by: pengliang.zhu
 * @Last Modified time: 2025-04-17 01:39:31
 */
import { Body, Controller, Get, Inject, Param, Post } from '@midwayjs/core';
import { WepayService } from '../service/wepay.service';
import { WeappService } from '../service/weapp.service';
import { Order } from '../entity';
import { OrderDiscountInfoService } from '../service/order-discount-info.service';
import { CustomError } from '../error/custom.error';
import { OrderStatus } from '../common/Constant';

@Controller('/wepay')
export class WePayController {
  @Inject()
  wepayService: WepayService;

  @Inject()
  weappService: WeappService;

  @Inject()
  orderDiscountInfoService: OrderDiscountInfoService;

  @Post('/jsapi', { summary: 'JSAPI/小程序下单' })
  async jsapi(
    @Body()
    {
      appid,
      sn,
    }: {
      /** appid */
      appid: string;
      /** 订单号 */
      sn: string;
    }
  ) {
    const signature = await this.wepayService.jsapi(appid, sn);
    return signature;
  }

  @Post('/pay_sign', { summary: '获取微信小程序支付签名' })
  async pay_sign(
    @Body()
    body: {
      appid: string;
      timeStamp: string;
      nonceStr: string;
      perpayId: string;
    }
  ) {
    return this.weappService.pay_sign(body);
  }

  @Get('/transactions/sn/:sn', {
    summary: '商户订单号查询订单',
    description:
      '订单支付成功后，商户可使用微信订单号查询订单或商户订单号查询订单；若订单未支付，则只能使用商户订单号查询订单。',
  })
  async transactions_sn(@Param('sn') sn: string) {
    return this.wepayService.getTransactionsBySNWithStatusSync(sn);
  }

  @Post('/refund/:sn', { summary: '退款' })
  async refund(@Param('sn') sn: string) {
    // 1. 查询订单信息
    const order = await Order.findOne({
      where: { sn },
      attributes: ['id', 'sn', 'totalFee', 'status'],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 2. 检查订单是否已开始服务
    const hasStarted = ![
      OrderStatus.待付款,
      OrderStatus.待接单,
      OrderStatus.待服务,
      OrderStatus.已出发,
    ].includes(order.status as OrderStatus);

    // 3. 如果订单未开始服务，处理优惠券和权益卡退回
    if (!hasStarted) {
      try {
        // 检查订单是否有优惠信息
        const refundStatus =
          await this.orderDiscountInfoService.checkOrderRefundStatus(order.id);
        if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
          await this.orderDiscountInfoService.refundOrderDiscounts(
            order.id,
            '退款前退回优惠'
          );
        }
      } catch (error) {
        throw new CustomError('处理订单优惠退回失败，请稍后重试');
      }
    }

    // 4. 执行退款操作
    return this.wepayService.refund(sn);
  }
}
