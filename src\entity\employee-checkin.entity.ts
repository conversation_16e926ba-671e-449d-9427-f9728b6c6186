import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Employee } from './employee.entity';

export interface EmployeeCheckInAttributes {
  /** 打卡记录ID */
  id: number;
  /** 员工ID */
  employeeId: number;
  /** 打卡照片链接数组，最多9张 */
  photos: string[];
  /** 打卡描述 */
  description?: string;
  /** 打卡地址 */
  address?: string;
  /** 经度 */
  longitude?: number;
  /** 纬度 */
  latitude?: number;
  /** 打卡时间 */
  checkInTime: Date;
  /** 关联的员工信息 */
  employee?: Employee;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'employee_checkins',
  timestamps: true,
  comment: '员工出车打卡记录表',
})
export class EmployeeCheckIn
  extends Model<EmployeeCheckInAttributes>
  implements EmployeeCheckInAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '打卡记录ID',
  })
  id: number;

  @ForeignKey(() => Employee)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '员工ID',
  })
  employeeId: number;

  @Column({
    type: DataType.JSON,
    allowNull: false,
    comment: '打卡照片链接数组，最多9张',
  })
  photos: string[];

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
    comment: '打卡描述',
  })
  description?: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '打卡地址',
  })
  address?: string;

  @Column({
    type: DataType.DECIMAL(10, 7),
    allowNull: true,
    comment: '经度',
  })
  longitude?: number;

  @Column({
    type: DataType.DECIMAL(10, 7),
    allowNull: true,
    comment: '纬度',
  })
  latitude?: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '打卡时间',
  })
  checkInTime: Date;

  @BelongsTo(() => Employee)
  employee: Employee;
}
