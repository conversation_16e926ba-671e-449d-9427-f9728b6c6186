# 员工出车拍照模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 员工打卡记录数据结构
```typescript
interface CheckInPhotos {
  vehicleExterior?: string[];    // 车辆外观照片，最多9张
  serviceStaff?: string[];       // 服务人员照片，最多9张
  vehicleInterior?: string[];    // 车内情况照片，最多9张
}

interface EmployeeCheckIn {
  id: number;                    // 打卡记录ID
  employeeId: number;            // 员工ID
  photos: CheckInPhotos;         // 分组照片对象
  description?: string;          // 打卡描述
  address?: string;              // 打卡地址
  longitude?: number;            // 经度
  latitude?: number;             // 纬度
  checkInTime: Date;             // 打卡时间
  employee?: Employee;           // 关联的员工信息
  createdAt?: Date;              // 创建时间
  updatedAt?: Date;              // 更新时间
}

interface Employee {
  id: number;                    // 员工ID
  name: string;                  // 真实姓名
  phone: string;                 // 手机号
  avatar?: string;               // 头像
  level?: number;                // 接单等级（1-5级）
  rating?: number;               // 服务评分（0-5分）
}
```

---

## 1. 员工端接口

### 1.1 员工打卡
**接口地址：** `POST /openapi/employee-checkins`
**接口描述：** 员工出车拍照打卡  
**是否需要认证：** 是  
**适用端：** 员工端

**请求体：**
```json
{
  "employeeId": 1,
  "photos": {
    "vehicleExterior": [
      "https://example.com/vehicle1.jpg",
      "https://example.com/vehicle2.jpg"
    ],
    "serviceStaff": [
      "https://example.com/staff1.jpg"
    ],
    "vehicleInterior": [
      "https://example.com/interior1.jpg",
      "https://example.com/interior2.jpg"
    ]
  },
  "description": "今日出车，准备开始服务",
  "address": "北京市朝阳区某某街道",
  "longitude": 116.397128,
  "latitude": 39.916527
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| photos | object | 是 | 分组照片对象 |
| photos.vehicleExterior | string[] | 否 | 车辆外观照片，最多9张 |
| photos.serviceStaff | string[] | 否 | 服务人员照片，最多9张 |
| photos.vehicleInterior | string[] | 否 | 车内情况照片，最多9张 |
| description | string | 否 | 打卡描述，最多500字符 |
| address | string | 否 | 打卡地址 |
| longitude | number | 否 | 经度 |
| latitude | number | 否 | 纬度 |

**照片分组说明：**
- **车辆外观(vehicleExterior)**：拍摄车辆外部状况，包括车身、轮胎等
- **服务人员(serviceStaff)**：拍摄服务人员工作状态，包括着装、工具准备等
- **车内情况(vehicleInterior)**：拍摄车内环境，包括座椅、设备等
- 每个分组最多支持9张照片，至少需要上传一个分组的照片

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "employeeId": 1,
    "photos": {
      "vehicleExterior": [
        "https://example.com/vehicle1.jpg",
        "https://example.com/vehicle2.jpg"
      ],
      "serviceStaff": [
        "https://example.com/staff1.jpg"
      ],
      "vehicleInterior": [
        "https://example.com/interior1.jpg",
        "https://example.com/interior2.jpg"
      ]
    },
    "description": "今日出车，准备开始服务",
    "address": "北京市朝阳区某某街道",
    "longitude": 116.397128,
    "latitude": 39.916527,
    "checkInTime": "2024-01-01T08:30:00.000Z",
    "employee": {
      "id": 1,
      "name": "张师傅",
      "phone": "13800138001",
      "avatar": "https://example.com/avatar1.jpg",
      "level": 3,
      "rating": 4.8
    },
    "createdAt": "2024-01-01T08:30:00.000Z"
  }
}
```

### 1.2 查询员工打卡记录列表
**接口地址：** `GET /openapi/employee-checkins/employee/{employeeId}`
**接口描述：** 查询指定员工的打卡记录列表  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "employeeId": 1,
        "photos": [
          "https://example.com/photo1.jpg",
          "https://example.com/photo2.jpg"
        ],
        "description": "今日出车，准备开始服务",
        "address": "北京市朝阳区某某街道",
        "longitude": 116.397128,
        "latitude": 39.916527,
        "checkInTime": "2024-01-01T08:30:00.000Z",
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001"
        }
      }
    ],
    "total": 25
  }
}
```

### 1.3 查询打卡记录详情
**接口地址：** `GET /openapi/employee-checkins/{id}`
**接口描述：** 查询打卡记录详情  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 打卡记录ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "employeeId": 1,
    "photos": {
      "vehicleExterior": [
        "https://example.com/vehicle1.jpg",
        "https://example.com/vehicle2.jpg"
      ],
      "serviceStaff": [
        "https://example.com/staff1.jpg"
      ],
      "vehicleInterior": [
        "https://example.com/interior1.jpg",
        "https://example.com/interior2.jpg"
      ]
    },
    "description": "今日出车，准备开始服务",
    "address": "北京市朝阳区某某街道",
    "longitude": 116.397128,
    "latitude": 39.916527,
    "checkInTime": "2024-01-01T08:30:00.000Z",
    "employee": {
      "id": 1,
      "name": "张师傅",
      "phone": "13800138001",
      "avatar": "https://example.com/avatar1.jpg",
      "level": 3,
      "rating": 4.8
    },
    "createdAt": "2024-01-01T08:30:00.000Z",
    "updatedAt": "2024-01-01T08:30:00.000Z"
  }
}
```

### 1.4 员工删除自己的打卡记录
**接口地址：** `DELETE /openapi/employee-checkins/{id}/employee/{employeeId}`
**接口描述：** 员工删除自己的打卡记录  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 打卡记录ID |
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 1.5 查询员工打卡统计
**接口地址：** `GET /openapi/employee-checkins/employee/{employeeId}/statistics`
**接口描述：** 查询员工打卡统计信息  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCount": 150,
    "todayCount": 3,
    "employeeCount": 1
  }
}
```

### 1.6 查询员工今日打卡记录
**接口地址：** `GET /openapi/employee-checkins/employee/{employeeId}/today`
**接口描述：** 查询员工今日打卡记录  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "employeeId": 1,
        "photos": [
          "https://example.com/photo1.jpg"
        ],
        "description": "早班出车",
        "checkInTime": "2024-01-01T08:30:00.000Z"
      },
      {
        "id": 2,
        "employeeId": 1,
        "photos": [
          "https://example.com/photo2.jpg"
        ],
        "description": "午班出车",
        "checkInTime": "2024-01-01T13:30:00.000Z"
      }
    ],
    "total": 2
  }
}
```

---

## 2. 管理端接口

### 2.1 查询所有员工打卡记录
**接口地址：** `GET /employee-checkins`
**接口描述：** 管理端查询所有员工打卡记录，支持分页和筛选
**是否需要认证：** 是
**适用端：** 管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| employeeId | number | 否 | 员工ID筛选 |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| keyword | string | 否 | 关键词搜索（员工姓名或手机号） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "employeeId": 1,
        "photos": [
          "https://example.com/photo1.jpg",
          "https://example.com/photo2.jpg"
        ],
        "description": "今日出车，准备开始服务",
        "address": "北京市朝阳区某某街道",
        "longitude": 116.397128,
        "latitude": 39.916527,
        "checkInTime": "2024-01-01T08:30:00.000Z",
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001",
          "avatar": "https://example.com/avatar1.jpg",
          "level": 3,
          "rating": 4.8
        },
        "createdAt": "2024-01-01T08:30:00.000Z"
      }
    ],
    "total": 500
  }
}
```

### 2.2 获取打卡统计信息
**接口地址：** `GET /employee-checkins/statistics`
**接口描述：** 管理端获取打卡统计信息
**是否需要认证：** 是
**适用端：** 管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 否 | 员工ID筛选 |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCount": 1500,
    "todayCount": 25,
    "employeeCount": 15
  }
}
```

### 2.3 查询打卡记录详情
**接口地址：** `GET /employee-checkins/{id}`
**接口描述：** 管理端查询打卡记录详情
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 打卡记录ID |

**响应示例：** 同员工端接口 1.3

### 2.4 删除打卡记录
**接口地址：** `DELETE /employee-checkins/{id}`
**接口描述：** 管理端删除打卡记录
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 打卡记录ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 2.5 查询指定员工打卡记录
**接口地址：** `GET /employee-checkins/employee/{employeeId}`
**接口描述：** 管理端查询指定员工打卡记录
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：** 同员工端接口 1.2

**响应示例：** 同员工端接口 1.2

### 2.6 查询指定员工打卡统计
**接口地址：** `GET /employee-checkins/employee/{employeeId}/statistics`
**接口描述：** 管理端查询指定员工打卡统计
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：** 同管理端接口 2.2

**响应示例：** 同管理端接口 2.2

### 2.7 批量删除打卡记录
**接口地址：** `POST /employee-checkins/batch-delete`
**接口描述：** 管理端批量删除打卡记录
**是否需要认证：** 是
**适用端：** 管理端

**请求体：**
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | number[] | 是 | 要删除的打卡记录ID数组 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "results": [
      { "id": 1, "success": true },
      { "id": 2, "success": true },
      { "id": 3, "success": false, "error": "打卡记录不存在" },
      { "id": 4, "success": true },
      { "id": 5, "success": true }
    ],
    "successCount": 4,
    "failCount": 1
  }
}
```

## 3. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录已过期 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 4. 注意事项

1. **照片数量限制**：每次打卡最多上传9张照片，至少上传1张照片
2. **权限控制**：员工只能查看和删除自己的打卡记录，管理端可以查看所有记录
3. **时间格式**：所有时间字段均使用ISO 8601格式（YYYY-MM-DDTHH:mm:ss.sssZ）
4. **地理位置**：经纬度为可选字段，用于记录打卡位置
5. **描述长度**：打卡描述最多500个字符
6. **数据关联**：打卡记录与员工信息关联，便于数据追溯和管理
7. **分页查询**：支持分页查询，默认每页10条记录
8. **日期筛选**：支持按日期范围筛选打卡记录
9. **关键词搜索**：管理端支持按员工姓名或手机号搜索
10. **统计功能**：提供总打卡次数、今日打卡次数、参与员工数量等统计信息
11. **批量操作**：管理端支持批量删除打卡记录
12. **数据安全**：员工只能删除自己的打卡记录，确保数据安全
