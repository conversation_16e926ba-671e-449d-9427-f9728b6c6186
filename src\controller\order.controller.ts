import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Del,
  Query,
  Put,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OrderService } from '../service/order.service';
import { ServicePhotoService } from '../service/service-photo.service';
import { CustomError } from '../error/custom.error';
import {
  AdditionalService,
  Customer,
  Employee,
  OrderDetail,
  Pet,
  Service,
  ServiceType,
  AdditionalServiceOrder,
  AdditionalServiceOrderDetail,
} from '../entity';
import { Op } from 'sequelize';
import { OrderStatus } from '../common/Constant';

@Controller('/orders')
export class OrderController {
  @Inject()
  ctx: Context;

  @Inject()
  service: OrderService;

  @Inject()
  servicePhotoService: ServicePhotoService;

  @Get('/', { summary: '查询所有订单' })
  async indexAll(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      filter,
      phone,
      employeename,
      includeAdditionalServices,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // // name支持模糊查询
    // if (queryInfo.name) {
    //   queryInfo.name = {
    //     [Op.like]: `%${queryInfo.name}%`,
    //   };
    // }
    // 构建OrderDetail的include
    const orderDetailInclude = [
      {
        model: AdditionalService,
        through: {
          attributes: [],
        },
      },
      {
        model: Pet,
      },
      {
        model: Service,
        attributes: ['id', 'serviceName'],
        include: [
          {
            model: ServiceType,
            attributes: ['id', 'name'],
          },
        ],
      },
    ];

    // 如果需要查询追加服务详情，添加追加服务关联
    if (includeAdditionalServices === 'true') {
      (orderDetailInclude as any).push({
        model: AdditionalServiceOrder,
        include: [
          {
            model: AdditionalServiceOrderDetail,
            include: [
              {
                model: Service,
                attributes: ['id', 'serviceName'],
              },
            ],
          },
        ],
      });
    }

    const include = [
      {
        model: OrderDetail,
        include: orderDetailInclude,
      },
      {
        model: Customer,
        where: phone
          ? {
              phone,
            }
          : undefined,
      },
      {
        model: Employee,
        where: employeename
          ? {
              name: { [Op.like]: `%${employeename}%` },
            }
          : undefined,
      },
    ];

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      filter,
      include,
      order: [['createdAt', 'DESC']],
    });
  }

  // 获取订单日志
  @Get('/:orderId/logs')
  async getLogs(@Param('orderId') orderId: number) {
    const res = await this.service.getLogs(orderId);
    return res;
  }

  // 查询可接单列表
  // list: '/orders/{employeeId}'
  @Get('/:employeeId')
  async list(
    @Param('employeeId') employeeId: number,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number,
    @Query('type') type: string
  ) {
    if (!employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.findList({
      page: current,
      pageSize,
      type,
      employeeId,
    });
    return res;
  }

  @Get('/employee/:employeeId', { summary: '按状态查询员工名下的订单列表' })
  async findEmployeeOrders(
    @Param('employeeId') employeeId: number,
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    return await this.service.findEmployeeOrders({
      employeeId,
      status: status.split(',') as OrderStatus[],
      page: current,
      pageSize,
    });
  }

  // 接单
  // accept: '/orders/{orderId}/accept',
  @Post('/:orderId/accept')
  async accept(
    @Param('orderId') orderId: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.acceptOrder(orderId, employeeId);
    return res;
  }

  // 派单
  // deliver: '/orders/{orderId}/deliver',
  @Post('/:orderId/deliver')
  async deliver(
    @Param('orderId') orderId: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.deliverOrder(orderId, employeeId);
    return res;
  }

  // 转单
  // transfer: '/orders/{orderId}/transfer',
  @Post('/:orderId/transfer')
  async transfer(
    @Param('orderId') orderId: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.transferOrder(orderId, employeeId);
    return res;
  }

  // 修改服务时间
  @Put('/:orderId/updateServiceTime')
  async updateServiceTime(
    @Param('orderId') orderId: number,
    @Body() body: { serviceTime: Date; employeeId?: number }
  ) {
    return await this.service.updateServiceTime({
      id: orderId,
      serviceTime: body.serviceTime,
      employeeId: body.employeeId,
    });
  }

  // 出发
  // dispatch: '/orders/{orderId}/dispatch',
  @Post('/:orderId/dispatch')
  async dispatch(
    @Param('orderId') orderId: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.order_dispatch(orderId, employeeId);
    return res;
  }

  // 开始服务
  // start: '/orders/{orderId}/start',
  @Post('/:orderId/start')
  async start(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; beforePhotos?: string[] }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.order_start(
      orderId,
      body.employeeId,
      body.beforePhotos
    );
    return res;
  }

  // 完成订单
  // complete: '/orders/{orderId}/complete',
  @Post('/:orderId/complete')
  async complete(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; afterPhotos?: string[] }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工id不能为空', 400);
    }
    const res = await this.service.order_complete(
      orderId,
      body.employeeId,
      body.afterPhotos
    );
    return res;
  }

  // 取消订单
  // cancel: '/orders/{orderId}/cancel',
  @Post('/:orderId/cancel')
  async cancel(@Param('orderId') orderId: number) {
    // 用户ID传0表示由后台取消
    const res = await this.service.cancelOrder(0, orderId);
    return res;
  }

  // 删除订单
  @Del('/:orderId')
  async delete(@Param('orderId') orderId: number) {
    const res = await this.service.deleteOrder(orderId);
    return res;
  }

  @Post('/:sn/auditRefund', { summary: '审核退款' })
  async auditRefund(
    @Param('sn') sn: string,
    @Body()
    {
      result,
      reason,
      money,
    }: { result: boolean; reason?: string; money?: number }
  ) {
    const res = await this.service.auditRefund({
      sn,
      result,
      reason,
      money,
    });
    return res;
  }

  // 设置服务前照片（完整替换模式，推荐使用）
  @Post('/:orderId/set-before-photos', {
    summary: '设置服务前照片（完整替换，支持添加/删除/重置）',
  })
  async setBeforePhotos(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; photoUrls: string[] }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    // 允许空数组来清空照片
    const photoUrls = body.photoUrls || [];
    const res = await this.servicePhotoService.setBeforePhotos(
      orderId,
      body.employeeId,
      photoUrls
    );
    return res;
  }

  // 上传服务前照片（增量模式，保持兼容性）
  @Post('/:orderId/upload-before-photos', {
    summary: '上传服务前照片（增量模式，最多9张）',
  })
  async uploadBeforePhotos(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; photoUrls: string[] }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    if (!body.photoUrls || body.photoUrls.length === 0) {
      throw new CustomError('照片链接不能为空', 400);
    }
    const res = await this.servicePhotoService.uploadBeforePhotos(
      orderId,
      body.employeeId,
      body.photoUrls
    );
    return res;
  }

  // 上传单张服务前照片（兼容性接口）
  @Post('/:orderId/upload-before-photo', { summary: '上传单张服务前照片' })
  async uploadBeforePhoto(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; photoUrl: string }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    if (!body.photoUrl) {
      throw new CustomError('照片链接不能为空', 400);
    }
    const res = await this.servicePhotoService.uploadBeforePhoto(
      orderId,
      body.employeeId,
      body.photoUrl
    );
    return res;
  }

  // 设置服务后照片（完整替换模式，推荐使用）
  @Post('/:orderId/set-after-photos', {
    summary: '设置服务后照片（完整替换，支持添加/删除/重置）',
  })
  async setAfterPhotos(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; photoUrls: string[] }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    // 允许空数组来清空照片
    const photoUrls = body.photoUrls || [];
    const res = await this.servicePhotoService.setAfterPhotos(
      orderId,
      body.employeeId,
      photoUrls
    );
    return res;
  }

  // 上传服务后照片（增量模式，保持兼容性）
  @Post('/:orderId/upload-after-photos', {
    summary: '上传服务后照片（增量模式，最多9张）',
  })
  async uploadAfterPhotos(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; photoUrls: string[] }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    if (!body.photoUrls || body.photoUrls.length === 0) {
      throw new CustomError('照片链接不能为空', 400);
    }
    const res = await this.servicePhotoService.uploadAfterPhotos(
      orderId,
      body.employeeId,
      body.photoUrls
    );
    return res;
  }

  // 上传单张服务后照片（兼容性接口）
  @Post('/:orderId/upload-after-photo', { summary: '上传单张服务后照片' })
  async uploadAfterPhoto(
    @Param('orderId') orderId: number,
    @Body() body: { employeeId: number; photoUrl: string }
  ) {
    if (!body.employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    if (!body.photoUrl) {
      throw new CustomError('照片链接不能为空', 400);
    }
    const res = await this.servicePhotoService.uploadAfterPhoto(
      orderId,
      body.employeeId,
      body.photoUrl
    );
    return res;
  }

  // 查询订单服务照片
  @Get('/:orderId/service-photos', { summary: '查询订单服务照片' })
  async getServicePhotos(@Param('orderId') orderId: number) {
    const res = await this.servicePhotoService.findByOrderId(orderId);
    return res;
  }
}
