# 追加服务功能说明

## 功能概述

追加服务功能允许用户在订单处于"服务中"状态时，为该订单申请额外的服务项目。整个流程包括用户申请、员工确认、用户付款等环节，支持使用折扣和代金券。

## 业务流程

### 1. 用户申请追加服务
- **触发条件：** 订单状态为"服务中"
- **操作步骤：**
  1. 用户在订单详情页选择需要追加的服务
  2. 选择服务数量
  3. 可选择使用权益卡或代金券
  4. 提交申请
- **结果：** 创建追加服务订单，状态为"待确认"

### 2. 员工确认阶段
- **操作人员：** 负责该订单的员工
- **操作选项：**
  - **确认：** 同意追加服务申请，状态变为"已确认"
  - **拒绝：** 拒绝申请并填写拒绝原因，状态变为"已拒绝"

### 3. 用户付款阶段
- **触发条件：** 员工确认后
- **操作步骤：**
  1. 用户收到确认通知
  2. 进行付款操作
  3. 付款成功后状态变为"已付款/服务中"
- **价格计算：** 实际支付金额 = 原价 - 权益卡抵扣 - 代金券抵扣

### 4. 退款处理
- **处理方式：** 通过主订单的退款申请流程
- **操作步骤：**
  1. 用户填写退款原因
  2. 管理人员与用户确认
  3. 按协商结果退回相应金额

## 数据结构

### 1. 主要实体

#### 追加服务订单 (AdditionalServiceOrder)
- 与订单详情关联，而不是与主订单关联
- 包含状态、价格、优惠信息等
- 支持员工确认/拒绝操作

#### 追加服务订单明细 (AdditionalServiceOrderDetail)
- 记录具体的服务项目和数量
- 冗余存储服务名称和价格，防止数据丢失

#### 追加服务优惠信息 (AdditionalServiceDiscountInfo)
- 记录使用的权益卡和代金券信息
- 支持退款时的优惠回退

### 2. 主订单冗余字段
- `hasAdditionalServices`: 是否有追加服务
- `additionalServiceAmount`: 追加服务总金额
- 用于提升查询性能，避免复杂的关联查询

## 接口设计

### 1. 用户端接口
- `POST /order-details/{orderDetailId}/additional-services` - 创建追加服务申请
- `GET /order-details/{orderDetailId}/additional-services` - 查询追加服务列表
- `GET /order-details/{orderDetailId}/additional-services/{id}` - 查询追加服务详情
- `POST /order-details/{orderDetailId}/additional-services/{id}/pay` - 支付追加服务

### 2. 员工端接口
- `GET /employee/additional-services/pending` - 查询待确认的追加服务列表
- `POST /order-details/{orderDetailId}/additional-services/{id}/confirm` - 确认追加服务
- `POST /order-details/{orderDetailId}/additional-services/{id}/reject` - 拒绝追加服务

### 3. 管理端接口
- `GET /orders?includeAdditionalServices=true` - 查询订单列表（包含追加服务详情）

## 部署说明

### 1. 数据库变更
执行 `docs/追加服务数据库脚本.sql` 中的SQL语句：
```bash
# 连接到数据库
mysql -u username -p database_name

# 执行脚本
source docs/追加服务数据库脚本.sql
```

### 2. 代码部署
1. 确保所有新增的实体类已正确导入
2. 重启应用服务
3. 验证接口是否正常工作

### 3. 测试验证
运行测试用例：
```bash
npm test src/test/additional-service-order.test.ts
```

## 性能优化

### 1. 查询优化
- 主订单列表默认只返回冗余字段（hasAdditionalServices、additionalServiceAmount）
- 只有在需要详细信息时才查询关联的追加服务数据
- 使用 `includeAdditionalServices=true` 参数控制是否返回详情

### 2. 索引优化
- 为追加服务订单的状态、员工ID、创建时间等字段创建索引
- 为主订单的追加服务相关字段创建复合索引

### 3. 缓存策略
- 可考虑对员工待确认的追加服务数量进行缓存
- 对主订单的追加服务统计信息进行缓存

## 注意事项

### 1. 业务限制
- 只有"服务中"状态的订单才能申请追加服务
- 员工只能操作自己负责的订单的追加服务
- 追加服务的退款必须通过主订单退款流程

### 2. 数据一致性
- 使用数据库事务确保追加服务订单和明细的一致性
- 支付成功后及时更新主订单的冗余字段
- 退款时需要同步处理优惠信息的回退

### 3. 错误处理
- 完善的参数验证和错误提示
- 状态检查确保操作的合法性
- 权限验证防止越权操作

## 扩展功能

### 1. 通知机制
- 集成现有的消息推送系统
- 支持WebSocket实时通知
- 微信小程序模板消息通知

### 2. 统计分析
- 追加服务的使用频率统计
- 员工确认率统计
- 追加服务收入分析

### 3. 审批流程
- 支持多级审批（如金额较大的追加服务需要管理员审批）
- 审批记录和日志
- 自动审批规则配置
