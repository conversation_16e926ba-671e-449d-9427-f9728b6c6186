# 员工出车拍照功能说明

## 功能概述

员工出车拍照功能是一个专门为员工提供的打卡系统，允许员工在出车时拍照记录工作状态，管理端可以实时查看员工的打卡情况。

## 主要特性

### 1. 员工端功能
- **拍照打卡**：支持上传1-9张照片
- **位置记录**：可选择记录打卡位置（经纬度和地址）
- **描述信息**：可添加打卡描述（最多500字符）
- **历史记录**：查看个人打卡历史
- **今日记录**：快速查看今日打卡情况
- **统计信息**：查看个人打卡统计

### 2. 管理端功能
- **全员监控**：查看所有员工打卡记录
- **筛选查询**：按员工、日期、关键词筛选
- **统计分析**：总体打卡统计信息
- **记录管理**：删除不当记录
- **批量操作**：批量删除记录

## 技术实现

### 数据库设计
```sql
CREATE TABLE `employee_checkins` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '打卡记录ID',
  `employeeId` int(11) NOT NULL COMMENT '员工ID',
  `photos` json NOT NULL COMMENT '打卡照片链接数组，最多9张',
  `description` varchar(500) DEFAULT NULL COMMENT '打卡描述',
  `address` varchar(255) DEFAULT NULL COMMENT '打卡地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `checkInTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '打卡时间',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_employee_id` (`employeeId`),
  KEY `idx_check_in_time` (`checkInTime`),
  CONSTRAINT `fk_employee_checkins_employee_id` FOREIGN KEY (`employeeId`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='员工出车打卡记录表';
```

### 核心文件
- **实体类**：`src/entity/employee-checkin.entity.ts`
- **服务类**：`src/service/employee-checkin.service.ts`
- **员工端控制器**：`src/controller/openapi/employee-checkin.controller.ts`
- **管理端控制器**：`src/controller/employee-checkin.controller.ts`
- **数据库迁移**：`src/migration/create-employee-checkins-table.sql`

## 部署说明

### 1. 数据库迁移
执行数据库迁移脚本创建新表：
```sql
source src/migration/create-employee-checkins-table.sql
```

### 2. 代码部署
新增的文件已经通过实体导出文件自动注册，无需额外配置。

### 3. 权限配置
根据需要为员工端和管理端配置相应的接口访问权限。

## 使用示例

### 员工打卡
```javascript
// POST /openapi/employee-checkins
{
  "employeeId": 1,
  "photos": [
    "https://example.com/photo1.jpg",
    "https://example.com/photo2.jpg"
  ],
  "description": "今日出车，准备开始服务",
  "address": "北京市朝阳区某某街道",
  "longitude": 116.397128,
  "latitude": 39.916527
}
```

### 查询员工记录
```javascript
// GET /openapi/employee-checkins/employee/1?current=1&pageSize=10&startDate=2024-01-01&endDate=2024-01-31
```

### 管理端查询
```javascript
// GET /employee-checkins?current=1&pageSize=10&keyword=张师傅&startDate=2024-01-01
```

## 业务规则

1. **照片限制**：每次打卡最少1张，最多9张照片
2. **权限控制**：员工只能查看和删除自己的记录
3. **数据关联**：打卡记录与员工信息关联，支持级联查询
4. **时间记录**：自动记录打卡时间，支持按时间范围筛选
5. **地理位置**：经纬度和地址为可选字段
6. **描述长度**：打卡描述最多500个字符

## 扩展功能

### 可能的扩展方向
1. **推送通知**：员工打卡时向管理端推送通知
2. **地理围栏**：限制打卡位置范围
3. **人脸识别**：结合人脸识别验证员工身份
4. **工作轨迹**：基于打卡记录生成员工工作轨迹
5. **考勤统计**：结合打卡数据生成考勤报表
6. **异常检测**：检测异常打卡行为

## 注意事项

1. **数据安全**：照片链接需要确保安全性，建议使用HTTPS
2. **存储优化**：大量照片可能影响数据库性能，建议定期清理
3. **隐私保护**：位置信息涉及隐私，需要合规处理
4. **网络依赖**：功能依赖网络连接，需要考虑离线场景
5. **性能监控**：监控接口性能，特别是照片上传相关接口
"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjcsInR5cGUiOiJlbXBsb3llZSIsImlhdCI6MTc1MDUxODc4NSwiZXhwIjoxNzUxMTIzNTg1fQ.6jSVvlHPk--KOo28s94ls1BYGJKilxUaxcyhfbSfXwI"