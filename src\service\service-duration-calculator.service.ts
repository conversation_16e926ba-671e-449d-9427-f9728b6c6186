import { Provide, Logger } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { ServiceChangeLog } from '../entity/service-change-log.entity';
import { Service } from '../entity/service.entity';
import { Order } from '../entity/order.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { OrderStatusChangeType } from '../common/Constant';
import { Op } from 'sequelize';

@Provide()
export class ServiceDurationCalculatorService {
  @Logger()
  logger: ILogger;

  /**
   * 计算并更新所有服务的平均时长
   */
  async updateAllServiceAvgDuration() {
    this.logger.info('开始更新所有服务的平均时长...');

    try {
      // 获取所有已发布的服务
      const services = await Service.findAll({
        where: { published: true },
        attributes: ['id', 'serviceName'],
      });

      let updatedCount = 0;
      for (const service of services) {
        try {
          const avgDuration = await this.calculateServiceAvgDuration(
            service.id
          );
          if (avgDuration !== null) {
            await service.update({ avgDuration });
            updatedCount++;
            this.logger.info(
              `更新服务 ${service.serviceName}(ID:${service.id}) 平均时长: ${avgDuration}分钟`
            );
          }
        } catch (error) {
          this.logger.error(
            `更新服务 ${service.serviceName}(ID:${service.id}) 平均时长失败:`,
            error
          );
        }
      }

      this.logger.info(`服务平均时长更新完成，共更新 ${updatedCount} 个服务`);
      return { success: true, updatedCount };
    } catch (error) {
      this.logger.error('更新所有服务平均时长失败:', error);
      throw error;
    }
  }

  /**
   * 计算单个服务的平均时长
   * @param serviceId 服务ID
   * @returns 平均时长（分钟），如果没有数据则返回null
   */
  async calculateServiceAvgDuration(serviceId: number): Promise<number | null> {
    try {
      // 查询包含该服务的最近100个已完成订单
      const orders = await Order.findAll({
        where: {
          status: '已完成',
        },
        include: [
          {
            model: OrderDetail,
            where: { serviceId },
            attributes: [],
          },
        ],
        attributes: ['id', 'updatedAt'],
        order: [['updatedAt', 'DESC']],
        limit: 100, // 限制最近100个订单
      });

      if (orders.length === 0) {
        this.logger.debug(`服务ID ${serviceId} 没有找到已完成的订单`);
        return null;
      }

      const orderIds = orders.map(order => order.id);

      // 查询这些订单的开始服务和完成订单日志
      const startLogs = await ServiceChangeLog.findAll({
        where: {
          orderId: { [Op.in]: orderIds },
          changeType: OrderStatusChangeType.开始服务,
        },
        attributes: ['orderId', 'createdAt'],
      });

      const endLogs = await ServiceChangeLog.findAll({
        where: {
          orderId: { [Op.in]: orderIds },
          changeType: OrderStatusChangeType.完成订单,
        },
        attributes: ['orderId', 'createdAt'],
      });

      // 创建日志映射
      const startLogMap = new Map<number, Date>();
      const endLogMap = new Map<number, Date>();

      startLogs.forEach(log => {
        startLogMap.set(log.orderId, log.createdAt);
      });

      endLogs.forEach(log => {
        endLogMap.set(log.orderId, log.createdAt);
      });

      // 计算每个订单的服务时长
      const durations: number[] = [];
      for (const orderId of orderIds) {
        const startTime = startLogMap.get(orderId);
        const endTime = endLogMap.get(orderId);

        if (startTime && endTime && endTime > startTime) {
          const durationMs = endTime.getTime() - startTime.getTime();
          const durationMinutes = Math.round(durationMs / (1000 * 60)); // 转换为分钟并四舍五入

          // console.log('durationMinutes', durationMinutes);
          // 过滤异常数据（小于5分钟或大于8小时的数据）
          if (durationMinutes >= 5 && durationMinutes <= 480) {
            durations.push(durationMinutes);
          }
        }
      }

      if (durations.length === 0) {
        this.logger.debug(`服务ID ${serviceId} 没有找到有效的时长数据`);
        return null;
      }

      // 计算平均时长
      const avgDuration = Math.round(
        durations.reduce((sum, duration) => sum + duration, 0) /
          durations.length
      );

      this.logger.debug(
        `服务ID ${serviceId} 计算结果: 有效订单${durations.length}个, 平均时长${avgDuration}分钟`
      );

      return avgDuration;
    } catch (error) {
      this.logger.error(`计算服务ID ${serviceId} 平均时长失败:`, error);
      throw error;
    }
  }

  /**
   * 计算单个服务的平均时长（对外接口）
   * @param serviceId 服务ID
   */
  async updateSingleServiceAvgDuration(serviceId: number) {
    this.logger.info(`开始更新服务ID ${serviceId} 的平均时长...`);

    try {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new Error(`服务ID ${serviceId} 不存在`);
      }

      const avgDuration = await this.calculateServiceAvgDuration(serviceId);
      if (avgDuration !== null) {
        await service.update({ avgDuration });
        this.logger.info(
          `更新服务 ${service.serviceName}(ID:${serviceId}) 平均时长: ${avgDuration}分钟`
        );
        return { success: true, avgDuration };
      } else {
        this.logger.info(
          `服务 ${service.serviceName}(ID:${serviceId}) 没有足够的数据计算平均时长`
        );
        return { success: true, avgDuration: null, message: '没有足够的数据' };
      }
    } catch (error) {
      this.logger.error(`更新服务ID ${serviceId} 平均时长失败:`, error);
      throw error;
    }
  }
}
