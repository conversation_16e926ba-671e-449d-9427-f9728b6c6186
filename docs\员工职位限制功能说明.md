# 员工职位限制功能说明

## 功能概述

为了规范员工接单范围，系统新增员工职位字段，并根据职位限制员工可接的订单类型：

- **洗护师**：只能接洗护类订单
- **美容师**：可以接洗护和美容类订单

## 数据库变更

### 员工表新增字段

```sql
ALTER TABLE employees ADD COLUMN position VARCHAR(50) COMMENT '职位';
```

### 字典数据

```sql
INSERT INTO dictionaries (type, code, name, sortOrder, status) VALUES
('员工职位', 'XI_HU_SHI', '洗护师', 1, 1),
('员工职位', 'MEI_RONG_SHI', '美容师', 2, 1);
```

## 业务规则

### 职位与服务类型映射

| 职位 | 可接服务类型 |
|------|-------------|
| 洗护师 | 洗护 |
| 美容师 | 洗护、美容 |

### 接单限制

1. **接单验证**：员工接单时，系统会验证订单中的服务类型是否在该员工职位允许范围内
2. **派单验证**：管理员派单时，同样会进行职位验证
3. **转单验证**：转单时也会验证目标员工的职位是否匹配
4. **可接单列表过滤**：员工查看可接单列表时，只显示其职位可接的订单

## 接口变更

### 员工管理接口

员工创建和更新接口现在支持 `position` 字段：

```json
{
  "name": "张三",
  "phone": "13800138000",
  "position": "XI_HU_SHI",
  "level": 3
}
```

### 可接单列表接口

`GET /orders/{employeeId}?type={type}`

**职位筛选逻辑**：
- 不管是否指定 `type` 参数，系统都会根据员工职位进行筛选
- 如果指定了 `type` 参数，会先验证该服务类型是否在员工职位允许范围内
  - 如果不在允许范围内，直接返回空结果
  - 如果在允许范围内，返回该类型的订单
- 如果没有指定 `type` 参数，返回员工职位允许的所有服务类型的订单

### 获取可选择员工接口

`GET /customers/{customerId}/employees?serviceTypeId={serviceTypeId}`

- 当传入 `serviceTypeId` 时，系统会根据服务类型筛选可以提供该服务的员工
- 只返回职位匹配的员工列表

### 接单相关接口

以下接口新增职位验证：

- `POST /orders/{orderId}/accept` - 接单
- `POST /orders/{orderId}/deliver` - 派单  
- `POST /orders/{orderId}/transfer` - 转单

如果员工职位不匹配订单服务类型，会返回错误：

```json
{
  "errCode": 400,
  "msg": "洗护师不能接此类订单"
}
```

## 错误处理

### 错误码说明

- 当洗护师尝试接美容订单时，返回：`洗护师不能接此类订单`
- 当美容师接单时，不会有职位限制错误（可接所有类型）

## 注意事项

1. 现有员工的 `position` 字段为空时，不进行职位限制验证
2. 建议为所有员工设置合适的职位
3. 新增员工时建议必填职位字段
4. 职位变更会立即影响员工的接单权限
