# 员工动作记录接口文档

## 概述
本文档描述了员工动作记录相关的API接口，基于订单状态变更记录统计员工的关键操作行为。

## 管理端接口

### 1. 获取员工动作记录列表

**接口地址：** `GET /employee-action-logs/list`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1,                     // 可选，员工ID
  "orderId": 1,                        // 可选，订单ID
  "changeType": "接单",                // 可选，动作类型
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "orderSn": "20240101001",
        "orderStatus": "已完成",
        "serviceTime": "2024-01-01T10:00:00.000Z",
        "totalFee": 100.00,
        "customer": {
          "id": 1,
          "nickname": "小明",
          "phone": "13800138002"
        },
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001"
        },
        "changeType": "接单",
        "changeTypeLabel": "接单",
        "description": null,
        "actionTime": "2024-01-01T09:30:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

### 2. 获取员工动作统计概览

**接口地址：** `GET /employee-action-logs/overview`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "totalActions": 500,               // 总动作数
    "actionStats": [
      {
        "changeType": "接单",
        "changeTypeLabel": "接单",
        "count": 100
      },
      {
        "changeType": "出发",
        "changeTypeLabel": "出发",
        "count": 95
      },
      {
        "changeType": "开始服务",
        "changeTypeLabel": "开始服务",
        "count": 95
      },
      {
        "changeType": "完成订单",
        "changeTypeLabel": "完成订单",
        "count": 90
      }
    ]
  }
}
```

### 3. 按员工统计动作记录

**接口地址：** `GET /employee-action-logs/stats-by-employee`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "employeeId": 1,
        "employeeName": "张师傅",
        "employeePhone": "13800138001",
        "totalActions": 200,
        "actionBreakdown": [
          {
            "changeType": "接单",
            "changeTypeLabel": "接单",
            "count": 50
          },
          {
            "changeType": "出发",
            "changeTypeLabel": "出发",
            "count": 48
          },
          {
            "changeType": "开始服务",
            "changeTypeLabel": "开始服务",
            "count": 48
          },
          {
            "changeType": "完成订单",
            "changeTypeLabel": "完成订单",
            "count": 45
          }
        ]
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

### 4. 获取员工动作时间线

**接口地址：** `GET /employee-action-logs/timeline`

**请求参数：**
```json
{
  "employeeId": 1,                     // 必填，员工ID
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "orderId": 1                         // 可选，订单ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "orderId": 1,
      "orderSn": "20240101001",
      "changeType": "接单",
      "changeTypeLabel": "接单",
      "description": null,
      "actionTime": "2024-01-01T09:30:00.000Z"
    },
    {
      "id": 2,
      "orderId": 1,
      "orderSn": "20240101001",
      "changeType": "出发",
      "changeTypeLabel": "出发",
      "description": null,
      "actionTime": "2024-01-01T10:00:00.000Z"
    },
    {
      "id": 3,
      "orderId": 1,
      "orderSn": "20240101001",
      "changeType": "开始服务",
      "changeTypeLabel": "开始服务",
      "description": null,
      "actionTime": "2024-01-01T10:30:00.000Z"
    },
    {
      "id": 4,
      "orderId": 1,
      "orderSn": "20240101001",
      "changeType": "完成订单",
      "changeTypeLabel": "完成订单",
      "description": null,
      "actionTime": "2024-01-01T11:30:00.000Z"
    }
  ]
}
```

## 字段说明

### 动作类型 (changeType)
- `接单`: 员工接受订单
- `派单`: 系统派发订单给员工
- `转单`: 订单转给其他员工
- `修改服务时间`: 修改预约服务时间
- `出发`: 员工出发前往服务地点
- `开始服务`: 员工开始提供服务
- `完成订单`: 员工完成服务
- `取消订单`: 取消订单
- `申请退款`: 申请退款
- `退款`: 处理退款

### 时间字段
- `actionTime`: 动作执行时间，ISO 8601格式
- `serviceTime`: 预约服务时间，ISO 8601格式

### 统计说明
- 只统计员工执行的动作，不包括客户操作
- 动作记录基于订单状态变更日志
- 按动作执行时间排序

## 注意事项

1. 员工动作记录基于 `service_change_logs` 表中 `employeeId` 不为空的记录
2. 时间线接口按动作时间正序排列，其他接口按时间倒序排列
3. 获取员工动作时间线接口必须提供员工ID参数
4. 所有时间均为服务器时间（UTC+8）
5. 动作统计可以用于分析员工工作效率和服务质量
