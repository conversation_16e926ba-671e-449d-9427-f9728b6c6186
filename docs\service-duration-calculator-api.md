# 服务平均时长计算接口文档

## 概述
本文档描述了服务平均时长计算相关的API接口。系统通过分析订单状态变更日志中的"开始服务"和"完成订单"时间点，计算每个服务的平均时长，并将结果存储在服务表中。

## 管理端接口

### 1. 手动更新所有服务的平均时长

**接口地址：** `POST /service-duration-calculator/update-all`

**请求参数：** 无

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "success": true,
    "updatedCount": 15
  }
}
```

### 2. 手动更新单个服务的平均时长

**接口地址：** `POST /service-duration-calculator/update/:serviceId`

**路径参数：**
- `serviceId`: 服务ID

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "success": true,
    "avgDuration": 75
  }
}
```

### 3. 计算单个服务的平均时长（不更新数据库）

**接口地址：** `GET /service-duration-calculator/calculate/:serviceId`

**路径参数：**
- `serviceId`: 服务ID

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "serviceId": 1,
    "avgDuration": 75,
    "message": "计算成功"
  }
}
```

## 服务列表接口变更

### 1. 客户端服务列表

**接口地址：** `GET /openapi/service/services/:typeId`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "serviceName": "宠物洗护",
      "logo": "https://example.com/logo.jpg",
      "description": "专业宠物洗护服务",
      "basePrice": 100.00,
      "petTypes": "dog",
      "size": "small",
      "hairType": "short",
      "distanceChargeFlag": false,
      "cardDiscountFlag": true,
      "orderIndex": 1,
      "avgDuration": 75
    }
  ]
}
```

### 2. 管理端服务列表

**接口地址：** `GET /service`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "serviceName": "宠物洗护",
        "logo": "https://example.com/logo.jpg",
        "description": "专业宠物洗护服务",
        "basePrice": 100.00,
        "avgDuration": 75,
        "published": true
      }
    ],
    "total": 1
  }
}
```

## 计算逻辑说明

### 数据来源
- 基于 `service_change_logs` 表中的订单状态变更记录
- 使用 "开始服务" 和 "完成订单" 两个时间点计算服务时长

### 计算规则
1. **数据范围**：只取最近100个已完成的订单
2. **时长计算**：结束时间 - 开始时间，转换为分钟并四舍五入
3. **异常过滤**：过滤掉小于5分钟或大于8小时的异常数据
4. **平均值**：对有效时长数据求平均值并四舍五入

### 更新机制
1. **定时任务**：每天凌晨2点自动更新所有服务的平均时长
2. **手动触发**：可通过管理接口手动更新单个或所有服务
3. **实时计算**：可通过计算接口实时获取结果（不更新数据库）

## 数据库变更

### 服务表新增字段
```sql
ALTER TABLE services 
ADD COLUMN avgDuration INT NULL COMMENT '平均服务时长（分钟）';
```

## 注意事项

1. **数据准确性**：计算结果基于历史订单数据，新服务可能没有足够的数据
2. **性能考虑**：限制查询最近100个订单，避免大数据量查询影响性能
3. **异常处理**：自动过滤异常时长数据，确保结果的合理性
4. **定时更新**：建议在业务低峰期（凌晨）执行定时更新任务
5. **手动更新**：可用于新服务上线后的首次计算或数据修正

## 使用场景

1. **服务预估**：为客户提供服务时长预估
2. **资源规划**：帮助安排员工工作计划
3. **服务优化**：分析服务效率，优化服务流程
4. **价格调整**：基于实际服务时长调整服务定价
