# 位置服务接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

---

## 1. 员工端位置服务

### 1.1 员工端上报当前位置
**接口地址：** `POST /openapi/location/updateEmployeeLocation`
**接口描述：** 员工端定时上报当前位置，自动更新关联车辆的位置信息（静默处理，不抛出业务异常）
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "employeeId": 1,
  "latitude": 39.916527,
  "longitude": 116.397128,
  "address": "北京市朝阳区某某街道"
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| latitude | number | 是 | 纬度（-90到90之间） |
| longitude | number | 是 | 经度（-180到180之间） |
| address | string | 否 | 当前地址描述 |

**响应示例（成功）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "employeeId": 1,
    "vehicleId": 5,
    "plateNumber": "京A12345",
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "北京市朝阳区某某街道",
    "updateTime": "2024-01-15T10:30:00.000Z",
    "message": "位置更新成功"
  }
}
```

**响应示例（失败但不抛异常）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": false,
    "message": "未绑定车辆"
  }
}
```

**字段说明：**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 操作是否成功 |
| employeeId | number | 员工ID（成功时返回） |
| vehicleId | number | 关联的车辆ID（成功时返回） |
| plateNumber | string | 车牌号（成功时返回） |
| latitude | number | 更新后的纬度（成功时返回） |
| longitude | number | 更新后的经度（成功时返回） |
| address | string | 地址描述（可选，成功时返回） |
| updateTime | string | 位置更新时间，ISO 8601格式（成功时返回） |
| message | string | 操作结果消息 |

---

## 2. 位置计算服务

### 2.1 计算两个经纬度之间的距离
**接口地址：** `GET /openapi/location/calculateDistance`  
**接口描述：** 计算两个经纬度坐标之间的直线距离（使用Haversine公式）  
**是否需要认证：** 否

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lat1 | number | 是 | 起点纬度 |
| lng1 | number | 是 | 起点经度 |
| lat2 | number | 是 | 终点纬度 |
| lng2 | number | 是 | 终点经度 |

**请求示例：**
```
GET /openapi/location/calculateDistance?lat1=39.916527&lng1=116.397128&lat2=39.906527&lng2=116.387128
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": 1250
}
```

**说明：** 返回值为距离，单位为米

### 2.2 查找附近车辆
**接口地址：** `GET /openapi/location/findNearbyVehicles`  
**接口描述：** 根据指定位置查找附近的空闲车辆，按距离排序  
**是否需要认证：** 否

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lat | number | 是 | 中心点纬度 |
| lng | number | 是 | 中心点经度 |
| radius | number | 否 | 搜索半径（米），默认10000米 |

**请求示例：**
```
GET /openapi/location/findNearbyVehicles?lat=39.916527&lng=116.397128&radius=5000
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 5,
      "plateNumber": "京A12345",
      "vehicleType": "小型车",
      "latitude": 39.916527,
      "longitude": 116.397128,
      "status": "空闲",
      "distance": 850,
      "employee": {
        "id": 1,
        "name": "张师傅",
        "phone": "13800138000"
      },
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    {
      "id": 8,
      "plateNumber": "京B67890",
      "vehicleType": "中型车",
      "latitude": 39.920000,
      "longitude": 116.400000,
      "status": "空闲",
      "distance": 1200,
      "employee": {
        "id": 3,
        "name": "李师傅",
        "phone": "13900139000"
      },
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-15T09:45:00.000Z"
    }
  ]
}
```

**字段说明：**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| distance | number | 与中心点的距离，单位为米 |
| status | string | 车辆状态（空闲/服务中） |
| employee | object | 关联的员工信息 |

---

## 3. 业务规则

### 3.1 位置上报规则
1. **员工验证**：必须是系统中存在的有效员工
2. **车辆绑定**：员工必须已绑定车辆才能进行位置上报
3. **坐标验证**：经纬度必须在有效范围内
   - 纬度：-90 到 90 度
   - 经度：-180 到 180 度
4. **自动更新**：员工位置上报会自动更新关联车辆的位置信息
5. **实时性**：建议员工端每30秒-1分钟上报一次位置
6. **静默处理**：位置上报采用静默处理模式，不会向前端抛出业务异常
   - 所有错误情况都会记录到日志中
   - 返回统一的成功响应格式，通过success字段标识实际结果
   - 前端可根据success字段判断上报是否成功，但不会收到异常中断

### 3.2 附近车辆查询规则
1. **状态筛选**：只返回状态为"空闲"的车辆
2. **距离排序**：按与中心点的距离从近到远排序
3. **数量限制**：最多返回10辆车辆
4. **半径限制**：默认搜索半径为10公里，可自定义

### 3.3 距离计算规则
1. **计算方法**：使用Haversine公式计算球面距离
2. **精度**：返回结果精确到米，四舍五入
3. **地球半径**：使用6371公里作为地球平均半径

---

## 4. 使用场景

### 4.1 员工端应用场景
- **实时位置追踪**：员工在工作期间定时上报位置
- **服务调度**：管理端根据员工位置进行订单分配
- **轨迹记录**：记录员工的工作轨迹用于考勤和分析

### 4.2 客户端应用场景
- **就近派单**：根据客户位置查找最近的空闲车辆
- **预估到达时间**：计算员工到客户位置的距离和时间
- **服务跟踪**：实时显示服务人员的位置信息

### 4.3 管理端应用场景
- **车辆监控**：实时查看所有车辆的位置分布
- **调度优化**：基于位置信息优化订单分配策略
- **数据分析**：分析员工工作区域和移动模式

---

## 5. 注意事项

1. **隐私保护**：位置信息属于敏感数据，需要严格的权限控制
2. **网络优化**：位置上报频率要平衡实时性和网络消耗
3. **电池优化**：移动端应合理控制GPS使用频率
4. **数据准确性**：建议结合GPS、网络定位等多种方式提高精度
5. **异常处理**：网络异常时应有重试机制和本地缓存
6. **时区处理**：所有时间字段统一使用UTC+8时区
7. **坐标系统**：统一使用WGS84坐标系（GPS坐标系）

## 6. 日志记录说明

### 6.1 位置上报日志
由于位置上报采用静默处理模式，所有错误和成功情况都会详细记录到日志中：

**成功日志示例：**
```
[INFO] 员工位置上报成功 {
  "employeeId": 1,
  "employeeName": "张师傅",
  "vehicleId": 5,
  "plateNumber": "京A12345",
  "latitude": 39.916527,
  "longitude": 116.397128,
  "address": "北京市朝阳区某某街道"
}
```

**失败日志示例：**
```
[WARN] 员工位置上报失败：员工未绑定车辆 {
  "employeeId": 1,
  "employeeName": "张师傅"
}
```

**异常日志示例：**
```
[ERROR] 员工位置上报异常 {
  "employeeId": 1,
  "latitude": 39.916527,
  "longitude": 116.397128,
  "address": "北京市朝阳区某某街道",
  "error": "Database connection failed",
  "stack": "Error stack trace..."
}
```

### 6.2 日志级别说明
| 日志级别 | 使用场景 | 说明 |
|----------|----------|------|
| INFO | 位置上报成功 | 记录成功的位置更新操作 |
| WARN | 业务验证失败 | 员工不存在、未绑定车辆、坐标无效等 |
| ERROR | 系统异常 | 数据库错误、网络异常等系统级错误 |
