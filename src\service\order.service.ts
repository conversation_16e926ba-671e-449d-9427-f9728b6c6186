import { ILogger, Inject, Provide } from '@midwayjs/core';
import {
  AdditionalService,
  Customer,
  Employee,
  Order,
  OrderDetail,
  Service,
  ServiceChangeLog,
  ServiceType,
} from '../entity';
import { Op } from 'sequelize';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import * as lodash from 'lodash';
import { CustomError } from '../error/custom.error';
import {
  OrderStatus,
  OrderStatusChangeType,
  POSITION_SERVICE_MAPPING,
} from '../common/Constant';
import { WepayService } from './wepay.service';
import { OrderDiscountInfoService } from './order-discount-info.service';
import { MessageBroadcastService } from './message-broadcast.service';
import { WeappService } from './weapp.service';
import { MessageHelper } from '../utils/message.helper';
import { ServicePhotoService } from './service-photo.service';
import { AdditionalServiceOrderService } from './additional-service-order.service';

@Provide()
export class OrderService extends BaseService<Order> {
  @Inject()
  ctx: Context;

  @Inject()
  wepayService: WepayService;

  @Inject()
  weappService: WeappService;

  @Inject()
  orderDiscountInfoService: OrderDiscountInfoService;

  @Inject()
  messageBroadcastService: MessageBroadcastService;

  @Inject()
  logger: ILogger;

  @Inject()
  messageHelper: MessageHelper;

  @Inject()
  servicePhotoService: ServicePhotoService;

  @Inject()
  additionalServiceOrderService: AdditionalServiceOrderService;

  constructor() {
    super('订单');
  }
  getModel = () => {
    return Order;
  };

  /**
   * 获取订单日志
   *
   * @param orderId 订单ID
   * @returns 日志
   */
  async getLogs(orderId: number) {
    return await ServiceChangeLog.findAll({
      where: {
        orderId,
      },
      include: [
        {
          model: Customer,
        },
        {
          model: Employee,
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * 创建订单
   *
   * @param {CreateOrderData} body 订单数据
   * @memberof OrderService
   */
  async createOrder(body: CreateOrderData) {
    // TODO: 要考虑数据量大的时候使用分页查询
    const { orderDetails, discountInfos, ...createInfo } = body;
    // 生成订单编号：当前时间戳 + 4位随机数
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    createInfo['sn'] = `${timestamp}${random}`;
    // 使用事务创建订单及订单详情
    const result = await Order.sequelize.transaction(async t => {
      console.log('createInfo', createInfo);
      const order = await Order.create(
        {
          ...createInfo,
          serviceTime: !createInfo.serviceTime ? null : createInfo.serviceTime,
        },
        { transaction: t }
      );

      await Promise.all(
        orderDetails.map(async orderDetail => {
          const { addServiceIds, ...addInfo } = orderDetail;

          // 如果没有提供服务名称和价格，从数据库查询并填充
          if (!addInfo.serviceName || !addInfo.servicePrice) {
            const service = await Service.findByPk(addInfo.serviceId);
            if (service) {
              addInfo.serviceName = service.serviceName;
              addInfo.servicePrice = service.basePrice;
            }
          }

          const detail = await OrderDetail.create(
            {
              ...addInfo,
              orderId: order.id,
            },
            { transaction: t }
          );
          if (addServiceIds?.length) {
            console.log('addServiceIds', addServiceIds);
            const flattenedServiceIds = lodash.flattenDeep(addServiceIds);
            console.log('flattenedServiceIds', flattenedServiceIds);
            await detail.$set('additionalServices', flattenedServiceIds, {
              transaction: t,
            });
          }
        })
      );

      ServiceChangeLog.create({
        orderId: order.id,
        changeType: OrderStatusChangeType.下单,
        customerId: createInfo.customerId,
      });

      return order;
    });

    // 处理卡券使用和优惠信息创建
    if (discountInfos && discountInfos.length > 0) {
      try {
        await this.orderDiscountInfoService.processOrderDiscounts(
          result.id,
          discountInfos
        );
      } catch (error) {
        this.logger.error('处理订单优惠信息失败:', error);
      }
    }

    return result;
  }

  // 支付订单
  async payOrder(customerId: number, sn: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.待接单,
    });

    // 广播新订单消息
    await this.messageBroadcastService.broadcastNewOrder(order);

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.付款,
      customerId,
    });

    return true;
  }

  // 申请退款
  async applyRefund(customerId: number, sn: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    console.log('order', order);
    if (
      !([OrderStatus.待服务, OrderStatus.已出发] as string[]).includes(
        order.status
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.退款中,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.申请退款,
      customerId,
    });
    return true;
  }

  /**
   * 审核退款
   *
   * @param {object} params 查询条件
   * @param {string} params.sn 订单编号
   * @param {boolean} params.result 审核结果
   * @param {string} [params.reason] 审核原因，审核不通过时必填
   * @param {number} [params.money] 退款金额，审核通过时必填
   * @returns res
   */
  async auditRefund({
    sn,
    result,
    reason,
    money,
  }: {
    sn: string;
    result: boolean;
    reason?: string;
    money?: number;
  }) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.退款中) {
      throw new CustomError('订单状态不正确');
    }
    if (result) {
      // 计算总订单金额（包含追加服务）
      const totalOrderAmount =
        Number(order.totalFee) + Number(order.additionalServiceAmount || 0);
      const isFullRefund = Number(money) >= totalOrderAmount;

      // 处理主订单卡券退回（只有全额退款时才退回优惠券）
      if (isFullRefund) {
        try {
          // 检查订单是否有优惠信息
          const refundStatus =
            await this.orderDiscountInfoService.checkOrderRefundStatus(
              order.id
            );
          if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
            await this.orderDiscountInfoService.refundOrderDiscounts(
              order.id,
              `退款审核通过，退款金额${money}元（全额退款）`
            );
          }
        } catch (error) {
          this.logger.error('处理订单卡券退回失败:', error);
        }
      }

      // 处理追加服务退款
      if (order.hasAdditionalServices && isFullRefund) {
        try {
          // 查询该订单的所有已支付追加服务
          const additionalServices =
            await this.additionalServiceOrderService.findAll({
              query: {
                orderDetailId:
                  order.orderDetails?.map(detail => detail.id) || [],
                status: 'paid',
              },
            });

          if (
            additionalServices &&
            additionalServices.list &&
            additionalServices.list.length > 0
          ) {
            for (const additionalService of additionalServices.list) {
              await this.additionalServiceOrderService.refundAdditionalServiceOrder(
                additionalService.id,
                isFullRefund
                  ? `主订单全额退款，退款金额${money}元`
                  : `主订单部分退款，退款金额${money}元（不退优惠券）`,
                isFullRefund // 只有全额退款时才退回优惠券
              );
            }
          }
        } catch (error) {
          this.logger.error('处理追加服务退款失败:', error);
        }
      }

      await this.wepayService.refund(sn, money);
      await order.update({
        status: OrderStatus.已退款,
      });

      // 清除该订单的微信订阅信息
      this.weappService.clearOrderSubscriptions(order.id.toString());

      // 广播取消订单消息
      await this.messageBroadcastService.broadcastCancelOrder(order.id);

      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `退款成功，退款金额${money}元。操作人：${this.ctx.state.user.name}`,
      });
    } else {
      await order.update({
        status: OrderStatus.待服务,
      });
      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `退款失败，原因：${reason} 操作人：${this.ctx.state.user.name}`,
      });
    }
  }

  /**
   * 取消订单
   * 提前六个小时，扣除20%，提前四个小时，扣除40%，提前两个小时扣除60%，到点取消扣除80%。
   *
   * @param {*} id 订单ID
   * @param customerId 用户ID
   * @param id 订单ID
   * @returns res
   */
  async cancelOrder(customerId: number, id: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('目前仅能自助取消待支付订单，特殊情况请联系客服');
    }

    // 处理主订单卡券退回（取消订单时总是全额退回）
    try {
      // 检查订单是否有优惠信息
      const refundStatus =
        await this.orderDiscountInfoService.checkOrderRefundStatus(id);
      if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
        await this.orderDiscountInfoService.refundOrderDiscounts(
          id,
          customerId ? '用户取消订单' : '系统取消订单'
        );
      }
    } catch (error) {
      this.logger.error('处理订单卡券退回失败:', error);
    }

    // 处理追加服务取消（取消订单时总是退回优惠券）
    if (order.hasAdditionalServices) {
      try {
        // 查询该订单的所有已支付追加服务
        const additionalServices =
          await this.additionalServiceOrderService.findAll({
            query: {
              orderDetailId: order.orderDetails?.map(detail => detail.id) || [],
              status: 'paid',
            },
          });

        if (
          additionalServices &&
          additionalServices.list &&
          additionalServices.list.length > 0
        ) {
          for (const additionalService of additionalServices.list) {
            await this.additionalServiceOrderService.refundAdditionalServiceOrder(
              additionalService.id,
              customerId ? '用户取消订单' : '系统取消订单',
              true // 取消订单时总是退回优惠券
            );
          }
        }
      } catch (error) {
        this.logger.error('处理追加服务取消失败:', error);
      }
    }

    await order.update({
      status: OrderStatus.已取消,
    });

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.取消订单,
      customerId: customerId || null,
      description: customerId ? '用户取消订单' : '系统取消订单',
    });

    return true;
  }

  /**
   * 删除订单
   * 一般只有具备足够权限的用户，如系统管理员或高级用户才能执行删除订单操作
   * 很多系统限制只能删除特定状态的订单，如未支付的订单。
   * 已支付、配送中、已完成的订单通常不支持删除。
   * 通过在线结账系统处理付款的订单或使用礼品卡付款的订单，只能存档不能删除
   */
  async deleteOrder(id: number) {
    console.log('id', id);
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.已取消) {
      throw new CustomError('订单状态不正确');
    }

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    return await order.destroy();
    // throw new CustomError('暂不支持删除订单');
  }

  // 查询可接单列表
  async findList(query: any) {
    const { page = 1, pageSize = 10, type, employeeId, ...rest } = query;
    void rest;
    const where_orderDetail = {};

    // 获取员工职位限制的服务类型
    let allowedServiceTypes: string[] = [];
    if (employeeId) {
      const employee = await Employee.findByPk(employeeId);
      if (employee && employee.position) {
        allowedServiceTypes = POSITION_SERVICE_MAPPING[employee.position] || [];
      }
    }

    // 如果员工有职位限制，必须根据职位筛选
    if (allowedServiceTypes.length > 0) {
      // 如果指定了type，先检查是否在员工允许范围内
      if (type) {
        if (!allowedServiceTypes.includes(type)) {
          // 指定的服务类型不在员工职位允许范围内，返回空结果
          return { list: [], total: 0 };
        }
        // 指定的服务类型在允许范围内，使用指定的type
        const services = await Service.findAll({
          where: { published: true },
          attributes: ['id'],
          include: [
            {
              model: ServiceType,
              where: { type },
              attributes: ['id'],
            },
          ],
        });
        where_orderDetail['serviceId'] = services.map(s => s.id);
      } else {
        // 没有指定type，使用员工职位允许的所有服务类型
        const services = await Service.findAll({
          where: { published: true },
          attributes: ['id'],
          include: [
            {
              model: ServiceType,
              where: { type: { [Op.in]: allowedServiceTypes } },
              attributes: ['id'],
            },
          ],
        });
        where_orderDetail['serviceId'] = services.map(s => s.id);
      }

      // 如果没有找到匹配的服务，返回空结果
      if (
        !where_orderDetail['serviceId'] ||
        where_orderDetail['serviceId'].length === 0
      ) {
        return { list: [], total: 0 };
      }
    } else if (type) {
      // 没有员工职位限制，但指定了服务类型
      const services = await Service.findAll({
        where: { published: true },
        attributes: ['id'],
        include: [
          {
            model: ServiceType,
            where: { type },
            attributes: ['id'],
          },
        ],
      });
      where_orderDetail['serviceId'] = services.map(s => s.id);

      if (services.length === 0) {
        return { list: [], total: 0 };
      }
    }
    // 如果既没有员工职位限制，也没有指定服务类型，则不过滤服务

    // 构建订单查询条件
    const orderWhere: any = {
      status: OrderStatus.待接单,
    };

    // 如果提供了员工ID，则只查询指定给该员工的订单或未指定员工的订单
    if (employeeId) {
      orderWhere[Op.or] = [
        { employeeId: employeeId }, // 指定给该员工的订单
        { employeeId: null }, // 未指定员工的订单（系统派单）
      ];
    } else {
      // 如果没有提供员工ID，只查询未指定员工的订单
      orderWhere.employeeId = null;
    }

    const res = await Order.findAndCountAll({
      where: orderWhere,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      include: [
        {
          model: OrderDetail,
          where: where_orderDetail,
          include: [
            {
              model: AdditionalService,
              through: {
                attributes: [],
              },
            },
            {
              model: Service,
            },
          ],
        },
        {
          model: Customer,
        },
      ],
    });
    return {
      list: res.rows,
      total: res.count,
    };
  }

  // 按状态查询员工名下的订单列表
  async findEmployeeOrders({
    employeeId,
    status,
    page,
    pageSize,
  }: {
    employeeId: number;
    status: OrderStatus[];
    page?: number;
    pageSize?: number;
  }) {
    console.log('status:', status);
    return await this.findAll({
      query: {
        employeeId,
        status,
      },
      offset: page && pageSize ? (page - 1) * pageSize : undefined,
      limit: pageSize || undefined,
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: AdditionalService,
              through: {
                attributes: [],
              },
            },
            {
              model: Service,
            },
          ],
        },
        {
          model: Customer,
        },
      ],
    });
  }

  // 修改服务时间
  async updateServiceTime({
    id,
    serviceTime,
    employeeId,
  }: {
    id: number;
    serviceTime: Date;
    employeeId?: number;
  }) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待服务) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      serviceTime,
    });
    this.weappService.sendOrderTimeChangeMessage(order.sn);
    await ServiceChangeLog.create({
      orderId: order.id,
      employeeId,
      changeType: OrderStatusChangeType.修改服务时间,
      description: employeeId ? '员工修改服务时间' : '系统修改服务时间',
    });
    return true;
  }

  // 接单
  // accept: '/orders/{orderId}/accept',
  async acceptOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待接单].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 验证员工职位是否可以接此类订单
    await this.validateEmployeePosition(id, employeeId);
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });

    // 发送微信模板消息
    this.weappService.sendOrderConfirmationMessage(order.sn);

    // 发送系统内消息通知给员工
    this.messageHelper.sendOrderAcceptedNotification(order.employeeId, {
      orderNo: order.sn,
    });

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.接单,
      employeeId,
    });
    return true;
  }

  // 派单
  async deliverOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待接单].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 验证员工职位是否可以接此类订单
    await this.validateEmployeePosition(id, employeeId);
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    this.weappService.sendOrderConfirmationMessage(order.sn);
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.派单,
      employeeId,
      description: '系统派单',
    });
    return true;
  }

  // 转单
  async transferOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待服务].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 验证员工职位是否可以接此类订单
    await this.validateEmployeePosition(id, employeeId);
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.转单,
      employeeId,
      description: '系统转单',
    });
    return true;
  }

  // 出发
  // dispatch: '/orders/{orderId}/dispatch',
  async order_dispatch(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (![OrderStatus.待服务].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.已出发,
      employeeId,
    });
    this.weappService.sendOrderStatusChangeMessage(
      order.sn,
      '服务人员已出发，请保持联系'
    );
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.出发,
      employeeId,
    });
    return true;
  }

  // 开始服务
  // start: '/orders/{orderId}/start',
  async order_start(id: number, employeeId: number, beforePhotos?: string[]) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (
      ![OrderStatus.已出发, OrderStatus.待服务].includes(
        order.status as OrderStatus
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    const actualServiceStartTime = new Date();
    await order.update({
      status: OrderStatus.服务中,
      employeeId,
      actualServiceStartTime,
    });

    // 如果提供了服务前照片，则上传
    if (beforePhotos && beforePhotos.length > 0) {
      try {
        await this.servicePhotoService.uploadBeforePhotos(
          id,
          employeeId,
          beforePhotos
        );
      } catch (error) {
        this.logger.error('上传服务前照片失败:', error);
      }
    }

    this.weappService.sendOrderStatusChangeMessage(
      order.sn,
      '您的订单已开始服务'
    );
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.开始服务,
      employeeId,
    });
    return true;
  }

  // 完成订单
  // complete: '/orders/{orderId}/complete',
  async order_complete(id: number, employeeId: number, afterPhotos?: string[]) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (![OrderStatus.服务中].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    const actualServiceEndTime = new Date();
    let actualServiceDuration = 0;

    // 计算服务时长（分钟）
    if (order.actualServiceStartTime) {
      actualServiceDuration = Math.round(
        (actualServiceEndTime.getTime() -
          order.actualServiceStartTime.getTime()) /
          (1000 * 60)
      );
    }

    await order.update({
      status: OrderStatus.已完成,
      employeeId,
      actualServiceEndTime,
      actualServiceDuration,
    });

    // 完成所有已支付的追加服务
    if (order.hasAdditionalServices) {
      try {
        await this.completeAdditionalServices(id);
      } catch (error) {
        this.logger.error('完成追加服务失败:', error);
      }
    }

    // 如果提供了服务后照片，则上传
    if (afterPhotos && afterPhotos.length > 0) {
      try {
        await this.servicePhotoService.uploadAfterPhotos(
          id,
          employeeId,
          afterPhotos
        );
      } catch (error) {
        this.logger.error('上传服务后照片失败:', error);
      }
    }

    // 发送微信模板消息
    this.weappService.sendOrderStatusChangeMessage(
      order.sn,
      '您的订单服务已完成'
    );

    // 发送系统内消息通知给员工
    this.messageHelper.sendOrderCompletedNotification(order.employeeId, {
      orderNo: order.sn,
    });

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    // this.weappService.sendOrderCompletionMessage(order.sn);
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.完成订单,
      employeeId,
    });
    return true;
  }

  /**
   * 完成订单的所有已支付追加服务
   */
  private async completeAdditionalServices(orderId: number) {
    const order = await Order.findByPk(orderId, {
      include: [OrderDetail],
    });

    if (!order || !order.orderDetails) {
      return;
    }

    // 查询该订单所有已支付的追加服务
    const additionalServices = await this.additionalServiceOrderService.findAll(
      {
        query: {
          orderDetailId: order.orderDetails.map(detail => detail.id),
          status: 'paid',
        },
      }
    );

    if (
      !additionalServices ||
      !additionalServices.list ||
      additionalServices.list.length === 0
    ) {
      return;
    }

    // 批量完成追加服务
    for (const additionalService of additionalServices.list) {
      try {
        await this.additionalServiceOrderService.completeAdditionalService(
          additionalService.id
        );
      } catch (error) {
        this.logger.error(`完成追加服务${additionalService.id}失败:`, error);
      }
    }
  }

  // 退款订单
  // 平台或商家会规定具体的退款政策，如按比例退款、全额退款等。比如美团民宿，房客在 "全额退款日" 之前取消预订，可全额退还线上支付的全部订单金额及押金费用，之后取消则按房东规定的交易规则执行，扣除部分 / 全部订单金额后返还剩余部分

  // 导出订单

  /**
   * 验证员工职位是否可以接指定订单
   */
  private async validateEmployeePosition(orderId: number, employeeId: number) {
    const employee = await Employee.findByPk(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在');
    }

    if (employee.position) {
      const allowedServiceTypes =
        POSITION_SERVICE_MAPPING[employee.position] || [];
      if (allowedServiceTypes.length > 0) {
        // 获取订单中的服务类型
        const orderWithDetails = await Order.findByPk(orderId, {
          include: [
            {
              model: OrderDetail,
              include: [
                {
                  model: Service,
                  include: [
                    {
                      model: ServiceType,
                    },
                  ],
                },
              ],
            },
          ],
        });

        if (orderWithDetails && orderWithDetails.orderDetails) {
          const orderServiceTypes = orderWithDetails.orderDetails
            .map(detail => detail.service?.serviceType?.type)
            .filter(Boolean);

          // 检查是否有不允许的服务类型
          const hasDisallowedService = orderServiceTypes.some(
            serviceType => !allowedServiceTypes.includes(serviceType)
          );

          if (hasDisallowedService) {
            const positionName =
              employee.position === 'XI_HU_SHI' ? '洗护师' : '美容师';
            throw new CustomError(`${positionName}不能接此类订单`);
          }
        }
      }
    }
  }
}
