import {
  Provide,
  Schedule,
  CommonSchedule,
  Inject,
  Logger,
} from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { ServiceDurationCalculatorService } from '../service/service-duration-calculator.service';

@Provide()
@Schedule({
  cron: '0 2 * * *', // 每天凌晨2点执行
  type: 'worker', // 指定某一个 worker 执行
})
export class ServiceDurationUpdateJob implements CommonSchedule {
  @Logger()
  logger: ILogger;

  @Inject()
  serviceDurationCalculatorService: ServiceDurationCalculatorService;

  async exec() {
    this.logger.info('开始执行服务平均时长更新定时任务...');

    try {
      const result =
        await this.serviceDurationCalculatorService.updateAllServiceAvgDuration();

      this.logger.info(
        `服务平均时长更新定时任务执行成功，更新了 ${result.updatedCount} 个服务`
      );

      return result;
    } catch (error) {
      this.logger.error('服务平均时长更新定时任务执行失败:', error);
      throw error;
    }
  }
}
