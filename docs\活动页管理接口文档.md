# 活动页管理接口文档

## 概述

小程序活动页管理功能，支持富文本内容或URL链接两种方式维护活动信息列表，同一时间只能发布一个活动。

## 管理端接口

### 1. 查询活动列表

**接口地址：** `GET /admin/activities`

**请求参数：**
```json
{
  "current": 1,           // 当前页码，默认1
  "pageSize": 10,         // 每页数量，默认10
  "title": "活动标题",     // 可选，活动标题模糊查询
  "target": "用户端",      // 可选，受众筛选（用户端/员工端）
  "isPublished": 1        // 可选，发布状态筛选（0-未发布，1-已发布）
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "春节活动",
        "content": "<p>活动详情...</p>",
        "target": "用户端",
        "coverImage": "https://example.com/cover.jpg",
        "status": 1,
        "isPublished": 1,
        "publishedAt": "2024-01-01T00:00:00.000Z",
        "sortOrder": 1,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 1
  }
}
```

### 2. 查询活动详情

**接口地址：** `GET /admin/activities/:id`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "春节活动",
    "content": "<p>活动详情...</p>",
    "target": "用户端",
    "coverImage": "https://example.com/cover.jpg",
    "isPublished": 1,
    "publishedAt": "2024-01-01T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 3. 创建活动

**接口地址：** `POST /admin/activities`

**请求参数：**
```json
{
  "title": "活动标题",                    // 必填，最大100字符
  "contentType": "content",              // 必填，内容类型（content-富文本，url-链接地址）
  "content": "<p>富文本内容</p>",         // 可选，富文本内容（contentType为content时必填）
  "url": "https://example.com/activity", // 可选，活动链接地址（contentType为url时必填）
  "target": "用户端",                    // 必填，受众（用户端/员工端）
  "coverImage": "https://example.com/cover.jpg" // 可选，封面图片链接
}
```

### 4. 更新活动

**接口地址：** `PUT /admin/activities/:id`

**请求参数：**（所有字段都是可选的）
```json
{
  "title": "新活动标题",
  "contentType": "url",                  // 内容类型（content-富文本，url-链接地址）
  "content": "<p>新的富文本内容</p>",     // 富文本内容
  "url": "https://example.com/new-activity", // 活动链接地址
  "target": "员工端",
  "coverImage": "https://example.com/new-cover.jpg"
}
```

### 5. 删除活动

**接口地址：** `DELETE /admin/activities/:id`

**注意：** 已发布的活动不能删除，需要先取消发布。

### 6. 发布活动

**接口地址：** `POST /admin/activities/:id/publish`

**功能说明：**
- 发布指定活动
- 同一受众（用户端/员工端）同时只能有一个活动发布
- 发布新活动时会自动取消同受众的其他已发布活动
- 只有启用状态的活动才能发布

### 7. 取消发布活动

**接口地址：** `POST /admin/activities/:id/unpublish`

### 8. 查询当前发布的活动

**接口地址：** `GET /admin/activities/published/current`

**请求参数：**
```json
{
  "target": "用户端"  // 可选，默认"用户端"
}
```

## 小程序端接口

### 1. 获取当前发布的活动

**接口地址：** `GET /openapi/activities/current`

**请求参数：**
```json
{
  "target": "用户端"  // 可选，默认"用户端"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "春节活动",
    "contentType": "content",
    "content": "<p>活动详情...</p>",
    "url": null,
    "coverImage": "https://example.com/cover.jpg",
    "publishedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**注意：** 如果没有发布的活动，返回 `data: null`

### 2. 获取活动详情

**接口地址：** `GET /openapi/activities/:id`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "春节活动",
    "contentType": "url",
    "content": null,
    "url": "https://example.com/activity-page",
    "coverImage": "https://example.com/cover.jpg",
    "publishedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**注意：** 只返回已发布且启用状态的活动，否则返回 `data: null`

## 数据库字段说明

### activities 表新增字段

- `contentType`: VARCHAR(20)，内容类型（content-富文本，url-链接地址）
- `url`: VARCHAR(500)，活动链接地址
- `isPublished`: TINYINT，是否发布（0-未发布，1-已发布）
- `publishedAt`: DATETIME，发布时间

## 业务规则

1. **唯一发布规则**：同一受众（用户端/员工端）同时只能有一个活动处于发布状态
2. **内容类型规则**：
   - `contentType` 为 `content` 时，`content` 字段必须填写，`url` 字段可为空
   - `contentType` 为 `url` 时，`url` 字段必须填写且格式正确，`content` 字段可为空
   - URL格式必须以 `http://` 或 `https://` 开头
3. **删除限制**：已发布的活动不能直接删除，需要先取消发布
4. **排序规则**：活动列表按创建时间降序排列
